#!/usr/bin/env python3
"""
测试ESM2模型加载
"""

import torch
import sys
import os
sys.path.append('.')

def test_esm2_direct():
    """直接测试ESM2模型加载"""
    print("🧬 直接测试ESM2模型加载...")
    
    try:
        from transformers import EsmModel, EsmTokenizer
        
        # 方法1: 使用模型名称
        print("\n1. 尝试使用模型名称加载...")
        try:
            model_name = "facebook/esm2_t33_650M_UR50D"
            model = EsmModel.from_pretrained(model_name)
            tokenizer = EsmTokenizer.from_pretrained(model_name)
            print(f"✓ 成功使用模型名称加载: {model_name}")
            print(f"✓ 模型维度: {model.config.hidden_size}")
            print(f"✓ 词汇表大小: {len(tokenizer)}")
            return True, model, tokenizer
        except Exception as e:
            print(f"❌ 模型名称加载失败: {e}")
        
        # 方法2: 使用本地路径
        print("\n2. 尝试使用本地路径加载...")
        try:
            local_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D/snapshots/255893e6608ab942fb16da47f62667c303c571d6"
            model = EsmModel.from_pretrained(local_path)
            tokenizer = EsmTokenizer.from_pretrained(local_path)
            print(f"✓ 成功使用本地路径加载")
            print(f"✓ 模型维度: {model.config.hidden_size}")
            print(f"✓ 词汇表大小: {len(tokenizer)}")
            return True, model, tokenizer
        except Exception as e:
            print(f"❌ 本地路径加载失败: {e}")
            
        return False, None, None
        
    except ImportError as e:
        print(f"❌ 无法导入transformers: {e}")
        return False, None, None

def test_esm2_inference(model, tokenizer):
    """测试ESM2推理"""
    print("\n🔬 测试ESM2推理...")
    
    try:
        # 测试序列
        test_sequence = "MKRISTTITTTITITTGNGAG"
        print(f"测试序列: {test_sequence}")
        
        # Tokenize
        inputs = tokenizer(test_sequence, return_tensors="pt", padding=True, truncation=True)
        print(f"✓ Tokenization成功")
        print(f"  输入形状: {inputs['input_ids'].shape}")
        
        # 推理
        with torch.no_grad():
            outputs = model(**inputs)
            features = outputs.last_hidden_state
            
        print(f"✓ 推理成功")
        print(f"  输出形状: {features.shape}")
        print(f"  特征维度: {features.size(-1)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 推理失败: {e}")
        return False

def test_our_model():
    """测试我们的模型"""
    print("\n🤖 测试我们的ESM2CodonOptimizer模型...")
    
    try:
        from esm2_codon_optimizer import ESM2CodonOptimizer
        
        # 创建模型
        model = ESM2CodonOptimizer(
            esm_model_path="facebook/esm2_t33_650M_UR50D",
            freeze_esm=True,
            fusion_dim=512
        )
        
        print(f"✓ 模型创建成功")
        print(f"✓ 模型参数总数: {sum(p.numel() for p in model.parameters()):,}")
        print(f"✓ 可训练参数: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
        
        # 测试前向传播
        test_protein = "MKRISTTITTTITITTGNGAG"
        test_codons = ["ATG", "AAA", "CGT", "ATT", "AGC", "ACT", "ACT", "ATT", "ACT", "ACT", "ACT", "ATT", "ACT", "ATT", "ACT", "ACT", "GGT", "AAT", "GGT", "GCG", "GGT"]
        
        predictions, mask = model([test_protein], [test_codons])
        print(f"✓ 前向传播成功")
        print(f"  预测结果类型: {type(predictions)}")
        print(f"  预测结果长度: {len(predictions)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 ESM2模型加载测试")
    print("=" * 50)
    
    # 测试直接加载
    success, model, tokenizer = test_esm2_direct()
    
    if success:
        # 测试推理
        inference_success = test_esm2_inference(model, tokenizer)
        
        if inference_success:
            print("\n✅ ESM2模型完全正常！")
        else:
            print("\n⚠️  ESM2模型加载成功但推理失败")
    else:
        print("\n❌ ESM2模型加载失败")
    
    # 测试我们的模型
    our_model_success = test_our_model()
    
    # 总结
    print(f"\n{'=' * 50}")
    print("🎯 测试总结")
    print("=" * 50)
    
    print(f"ESM2直接加载: {'✅ 成功' if success else '❌ 失败'}")
    if success:
        print(f"ESM2推理测试: {'✅ 成功' if inference_success else '❌ 失败'}")
    print(f"我们的模型测试: {'✅ 成功' if our_model_success else '❌ 失败'}")
    
    if success and our_model_success:
        print("\n🎉 所有测试通过！ESM2模型配置正确，可以开始训练。")
        print("\n建议的训练命令:")
        print("python train_gpu_fixed.py --batch_size 8 --num_epochs 10 --max_length 256")
    else:
        print("\n⚠️  存在问题，需要进一步调试。")

if __name__ == "__main__":
    main()
