#!/usr/bin/env python3
"""
检查ESM2模型文件完整性
"""

import os
import json

def check_model_files():
    """检查模型文件"""
    print("🔍 检查ESM2模型文件...")
    
    base_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D"
    snapshot_path = os.path.join(base_path, "snapshots/255893e6608ab942fb16da47f62667c303c571d6")
    
    print(f"检查路径: {snapshot_path}")
    
    # 检查必要文件
    required_files = [
        "config.json",
        "pytorch_model.bin",
        "tokenizer_config.json",
        "vocab.txt"
    ]
    
    optional_files = [
        "model.safetensors",
        "tf_model.h5",
        "special_tokens_map.json"
    ]
    
    print("\n必要文件检查:")
    all_required_exist = True
    for file in required_files:
        file_path = os.path.join(snapshot_path, file)
        exists = os.path.exists(file_path)
        size = os.path.getsize(file_path) if exists else 0
        print(f"  {file}: {'✓' if exists else '❌'} ({size:,} bytes)")
        if not exists:
            all_required_exist = False
    
    print("\n可选文件检查:")
    for file in optional_files:
        file_path = os.path.join(snapshot_path, file)
        exists = os.path.exists(file_path)
        size = os.path.getsize(file_path) if exists else 0
        print(f"  {file}: {'✓' if exists else '❌'} ({size:,} bytes)")
    
    # 检查config.json内容
    config_path = os.path.join(snapshot_path, "config.json")
    if os.path.exists(config_path):
        print("\nconfig.json内容:")
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            print(f"  模型类型: {config.get('model_type', 'unknown')}")
            print(f"  隐藏层维度: {config.get('hidden_size', 'unknown')}")
            print(f"  层数: {config.get('num_hidden_layers', 'unknown')}")
            print(f"  注意力头数: {config.get('num_attention_heads', 'unknown')}")
            print(f"  词汇表大小: {config.get('vocab_size', 'unknown')}")
        except Exception as e:
            print(f"  ❌ 无法读取config.json: {e}")
    
    return all_required_exist

def test_simple_import():
    """测试简单导入"""
    print("\n🧪 测试transformers导入...")
    
    try:
        from transformers import EsmModel, EsmTokenizer
        print("✓ transformers导入成功")
        
        # 检查是否可以创建配置
        from transformers import EsmConfig
        print("✓ EsmConfig导入成功")
        
        return True
    except Exception as e:
        print(f"❌ transformers导入失败: {e}")
        return False

def test_config_loading():
    """测试配置加载"""
    print("\n⚙️  测试配置加载...")
    
    try:
        from transformers import EsmConfig
        
        # 方法1: 使用模型名称
        try:
            config = EsmConfig.from_pretrained("facebook/esm2_t33_650M_UR50D")
            print("✓ 使用模型名称加载配置成功")
            print(f"  隐藏层维度: {config.hidden_size}")
            return True
        except Exception as e:
            print(f"❌ 模型名称加载配置失败: {e}")
        
        # 方法2: 使用本地路径
        try:
            local_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D/snapshots/255893e6608ab942fb16da47f62667c303c571d6"
            config = EsmConfig.from_pretrained(local_path)
            print("✓ 使用本地路径加载配置成功")
            print(f"  隐藏层维度: {config.hidden_size}")
            return True
        except Exception as e:
            print(f"❌ 本地路径加载配置失败: {e}")
            
        return False
        
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 ESM2模型文件完整性检查")
    print("=" * 50)
    
    # 检查文件
    files_ok = check_model_files()
    
    # 测试导入
    import_ok = test_simple_import()
    
    # 测试配置
    config_ok = test_config_loading()
    
    # 总结
    print(f"\n{'=' * 50}")
    print("🎯 检查总结")
    print("=" * 50)
    
    print(f"模型文件完整性: {'✅ 完整' if files_ok else '❌ 不完整'}")
    print(f"transformers导入: {'✅ 成功' if import_ok else '❌ 失败'}")
    print(f"配置加载测试: {'✅ 成功' if config_ok else '❌ 失败'}")
    
    if files_ok and import_ok and config_ok:
        print("\n🎉 ESM2模型文件完整，应该可以正常加载！")
        print("模型加载可能需要较长时间，请耐心等待。")
    else:
        print("\n⚠️  存在问题，可能影响模型加载。")
        
        if not files_ok:
            print("建议重新下载ESM2模型文件。")
        if not import_ok:
            print("建议检查transformers库安装。")
        if not config_ok:
            print("建议检查模型路径和权限。")

if __name__ == "__main__":
    main()
