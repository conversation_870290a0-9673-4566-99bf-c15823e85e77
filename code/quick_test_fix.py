#!/usr/bin/env python3
"""
快速测试修复后的模型
"""

import torch
import sys
import os
sys.path.append('.')

def quick_test():
    """快速测试修复"""
    print("🚀 快速测试ESM2CodonOptimizer修复...")
    
    try:
        from esm2_codon_optimizer import ESM2CodonOptimizer
        
        # 创建模型（使用本地路径避免网络问题）
        print("创建模型...")
        model = ESM2CodonOptimizer(
            esm_model_path="/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D/snapshots/255893e6608ab942fb16da47f62667c303c571d6",
            freeze_esm=True,
            fusion_dim=256  # 使用较小的维度加快测试
        )
        
        print(f"✓ 模型创建成功")
        
        # 移动到GPU（如果可用）
        device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)
        print(f"✓ 模型移动到设备: {device}")
        
        # 测试简单序列
        test_protein = "MKRISTTITTTITITTGNGAG"
        test_codons = ["ATG", "AAA", "CGT", "ATT", "AGC", "ACT", "ACT", "ATT", "ACT", "ACT", "ACT", "ATT", "ACT", "ATT", "ACT", "ACT", "GGT", "AAT", "GGT", "GCG", "GGT"]
        
        print(f"测试序列: {test_protein}")
        print(f"测试密码子数量: {len(test_codons)}")
        
        # 前向传播测试
        print("\n测试前向传播...")
        with torch.no_grad():
            predictions, mask = model([test_protein], [test_codons])
        
        print(f"✓ 前向传播成功！")
        print(f"  预测结果类型: {type(predictions)}")
        print(f"  预测结果长度: {len(predictions)}")
        
        if len(predictions) > 0:
            print(f"  第一个样本预测长度: {len(predictions[0])}")
            
            # 检查前几个预测张量
            for i in range(min(3, len(predictions[0]))):
                pred_tensor = predictions[0][i]
                print(f"  位置 {i}: 形状={pred_tensor.shape}, 设备={pred_tensor.device}")
        
        # 测试密码子预测
        print("\n测试密码子预测...")
        optimal_codons = model.predict_optimal_codons(test_protein)
        print(f"✓ 密码子预测成功！")
        print(f"  优化后密码子数量: {len(optimal_codons)}")
        print(f"  优化后密码子: {optimal_codons[:10]}...")  # 只显示前10个
        
        # 验证翻译
        from Bio.Seq import Seq
        optimized_dna = ''.join(optimal_codons)
        translated = str(Seq(optimized_dna).translate())
        print(f"  翻译验证: {translated == test_protein}")
        
        print(f"\n🎉 所有测试通过！ESM2模型配置成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = quick_test()
    
    if success:
        print("\n✅ ESM2模型完全正常，可以开始训练！")
        print("\n推荐的训练命令:")
        print("python train_gpu_fixed.py --batch_size 8 --num_epochs 10 --max_length 256")
    else:
        print("\n❌ 仍有问题需要解决。")
