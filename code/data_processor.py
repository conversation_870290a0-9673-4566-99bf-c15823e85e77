"""
数据处理模块：处理大肠杆菌BL21蛋白质-核酸序列数据
"""

import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from typing import List, Tuple, Dict
import re
from Bio.Seq import Seq
try:
    from Bio.SeqUtils import GC
except ImportError:
    # 如果导入失败，使用自定义GC计算函数
    def GC(seq):
        """计算GC含量"""
        seq = seq.upper()
        gc_count = seq.count('G') + seq.count('C')
        return (gc_count / len(seq)) * 100 if len(seq) > 0 else 0
import random
from tqdm import tqdm

class EcoliCodonDataset(Dataset):
    """大肠杆菌密码子优化数据集"""
    
    def __init__(self, csv_file: str, max_length: int = 512, mask_ratio: float = 0.15):
        """
        Args:
            csv_file: CSV文件路径
            max_length: 最大序列长度
            mask_ratio: 掩码比例（STREAM策略）
        """
        self.max_length = max_length
        self.mask_ratio = mask_ratio
        
        # 读取数据
        print(f"Loading data from {csv_file}...")
        self.data = pd.read_csv(csv_file)
        
        # 数据清洗
        self.data = self._clean_data()
        
        print(f"Loaded {len(self.data)} sequences")
        print(f"Average protein length: {self.data['protein_length'].mean():.1f}")
        print(f"Average nucleotide length: {self.data['nucleotide_length'].mean():.1f}")
        
    def _clean_data(self):
        """数据清洗"""
        print("Cleaning data...")
        
        # 移除空值
        data = self.data.dropna()
        
        # 移除过短或过长的序列
        data = data[
            (data['protein_length'] >= 10) & 
            (data['protein_length'] <= self.max_length) &
            (data['nucleotide_length'] >= 30) &
            (data['nucleotide_length'] <= self.max_length * 3)
        ]
        
        # 验证序列有效性
        valid_indices = []
        for idx, row in tqdm(data.iterrows(), total=len(data), desc="Validating sequences"):
            if self._validate_sequence_pair(row['protein_sequence'], row['nucleotide_sequence']):
                valid_indices.append(idx)
                
        data = data.loc[valid_indices].reset_index(drop=True)
        
        print(f"After cleaning: {len(data)} sequences")
        return data
        
    def _validate_sequence_pair(self, protein_seq: str, nucleotide_seq: str) -> bool:
        """验证蛋白质-核酸序列对的有效性"""
        try:
            # 基本长度检查
            if len(protein_seq) < 10 or len(nucleotide_seq) < 30:
                return False

            # 检查蛋白质序列是否只包含标准氨基酸
            valid_aa = set('ARNDCQEGHILKMFPSTWYV')
            if not all(aa in valid_aa for aa in protein_seq):
                return False

            # 检查核酸序列是否只包含标准碱基
            valid_nt = set('ATCG')
            if not all(nt in valid_nt for nt in nucleotide_seq):
                return False

            # 简化的长度检查（允许一定的误差）
            expected_min_length = len(protein_seq) * 3
            expected_max_length = len(protein_seq) * 3 + 6  # 允许终止密码子等
            if not (expected_min_length <= len(nucleotide_seq) <= expected_max_length):
                return False

            return True

        except Exception:
            return False
            
    def _nucleotide_to_codons(self, nucleotide_seq: str) -> List[str]:
        """将核酸序列转换为密码子列表"""
        codons = []
        for i in range(0, len(nucleotide_seq), 3):
            codon = nucleotide_seq[i:i+3]
            if len(codon) == 3:
                codons.append(codon)
        return codons
        
    def _apply_stream_masking(self, codons: List[str]) -> Tuple[List[str], List[int]]:
        """应用STREAM掩码策略"""
        masked_codons = codons.copy()
        mask_positions = []
        
        # 随机选择要掩码的位置
        num_mask = int(len(codons) * self.mask_ratio)
        mask_indices = random.sample(range(len(codons)), min(num_mask, len(codons)))
        
        for idx in mask_indices:
            mask_positions.append(idx)
            # 用特殊token替换（这里用'XXX'表示）
            masked_codons[idx] = 'XXX'
            
        return masked_codons, mask_positions
        
    def __len__(self):
        return len(self.data)
        
    def __getitem__(self, idx):
        row = self.data.iloc[idx]
        
        protein_seq = row['protein_sequence']
        nucleotide_seq = row['nucleotide_sequence']
        
        # 转换为密码子序列
        original_codons = self._nucleotide_to_codons(nucleotide_seq)
        
        # 应用STREAM掩码
        masked_codons, mask_positions = self._apply_stream_masking(original_codons)
        
        return {
            'protein_sequence': protein_seq,
            'original_codons': original_codons,
            'masked_codons': masked_codons,
            'mask_positions': mask_positions,
            'protein_length': len(protein_seq),
            'nucleotide_length': len(nucleotide_seq)
        }

def collate_fn(batch):
    """自定义批处理函数"""
    protein_sequences = [item['protein_sequence'] for item in batch]
    original_codons = [item['original_codons'] for item in batch]
    masked_codons = [item['masked_codons'] for item in batch]
    mask_positions = [item['mask_positions'] for item in batch]
    
    return {
        'protein_sequences': protein_sequences,
        'original_codons': original_codons,
        'masked_codons': masked_codons,
        'mask_positions': mask_positions
    }

class CodonUsageAnalyzer:
    """密码子使用分析器"""
    
    def __init__(self, dataset: EcoliCodonDataset):
        self.dataset = dataset
        self.codon_frequencies = {}
        self.aa_codon_preferences = {}
        
    def analyze_codon_usage(self):
        """分析密码子使用频率"""
        print("Analyzing codon usage...")
        
        codon_counts = {}
        aa_codon_counts = {}
        
        for idx in tqdm(range(len(self.dataset))):
            item = self.dataset[idx]
            protein_seq = item['protein_sequence']
            codons = item['original_codons']
            
            for aa, codon in zip(protein_seq, codons):
                # 统计密码子频率
                codon_counts[codon] = codon_counts.get(codon, 0) + 1
                
                # 统计氨基酸-密码子对应关系
                if aa not in aa_codon_counts:
                    aa_codon_counts[aa] = {}
                aa_codon_counts[aa][codon] = aa_codon_counts[aa].get(codon, 0) + 1
                
        # 计算频率
        total_codons = sum(codon_counts.values())
        self.codon_frequencies = {
            codon: count / total_codons 
            for codon, count in codon_counts.items()
        }
        
        # 计算氨基酸特异性密码子偏好
        for aa, codon_counts_aa in aa_codon_counts.items():
            total_aa = sum(codon_counts_aa.values())
            self.aa_codon_preferences[aa] = {
                codon: count / total_aa 
                for codon, count in codon_counts_aa.items()
            }
            
        return self.codon_frequencies, self.aa_codon_preferences
        
    def get_codon_adaptation_index(self, codons: List[str]) -> float:
        """计算密码子适应指数（CAI）"""
        if not self.aa_codon_preferences:
            self.analyze_codon_usage()
            
        cai_values = []
        
        for codon in codons:
            # 找到对应的氨基酸
            aa = None
            for amino_acid, codon_list in ECOLI_CODON_TABLE.items():
                if codon in codon_list:
                    aa = amino_acid
                    break
                    
            if aa and aa in self.aa_codon_preferences:
                # 获取该氨基酸的最高频率密码子
                max_freq = max(self.aa_codon_preferences[aa].values())
                current_freq = self.aa_codon_preferences[aa].get(codon, 0)
                
                if max_freq > 0:
                    relative_freq = current_freq / max_freq
                    cai_values.append(relative_freq)
                    
        return np.exp(np.mean(np.log(cai_values))) if cai_values else 0.0
        
    def calculate_gc_content(self, codons: List[str]) -> float:
        """计算GC含量"""
        nucleotide_seq = ''.join(codons)
        return GC(nucleotide_seq) / 100.0
        
    def print_statistics(self):
        """打印统计信息"""
        if not self.codon_frequencies:
            self.analyze_codon_usage()
            
        print("\n=== Codon Usage Statistics ===")
        print(f"Total unique codons: {len(self.codon_frequencies)}")
        
        # 打印最常用的密码子
        sorted_codons = sorted(
            self.codon_frequencies.items(), 
            key=lambda x: x[1], 
            reverse=True
        )
        
        print("\nTop 10 most frequent codons:")
        for codon, freq in sorted_codons[:10]:
            print(f"{codon}: {freq:.4f}")
            
        # 打印每个氨基酸的密码子偏好
        print("\nCodon preferences by amino acid:")
        for aa in sorted(self.aa_codon_preferences.keys()):
            if aa != '*':  # 跳过终止密码子
                prefs = self.aa_codon_preferences[aa]
                sorted_prefs = sorted(prefs.items(), key=lambda x: x[1], reverse=True)
                print(f"{aa}: {sorted_prefs}")

# 从esm2_codon_optimizer.py导入密码子表
from esm2_codon_optimizer import ECOLI_CODON_TABLE

def create_data_loaders(csv_file: str, batch_size: int = 8, train_ratio: float = 0.8, 
                       max_length: int = 512, mask_ratio: float = 0.15):
    """创建训练和验证数据加载器"""
    
    # 创建数据集
    dataset = EcoliCodonDataset(csv_file, max_length, mask_ratio)
    
    # 分析密码子使用情况
    analyzer = CodonUsageAnalyzer(dataset)
    analyzer.print_statistics()
    
    # 划分训练和验证集
    total_size = len(dataset)
    train_size = int(total_size * train_ratio)
    val_size = total_size - train_size
    
    train_dataset, val_dataset = torch.utils.data.random_split(
        dataset, [train_size, val_size]
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, 
        batch_size=batch_size, 
        shuffle=True, 
        collate_fn=collate_fn,
        num_workers=2
    )
    
    val_loader = DataLoader(
        val_dataset, 
        batch_size=batch_size, 
        shuffle=False, 
        collate_fn=collate_fn,
        num_workers=2
    )
    
    print(f"\nDataset split:")
    print(f"Training samples: {len(train_dataset)}")
    print(f"Validation samples: {len(val_dataset)}")
    
    return train_loader, val_loader, analyzer
