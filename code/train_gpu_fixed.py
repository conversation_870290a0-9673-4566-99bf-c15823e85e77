#!/usr/bin/env python3
"""
修复设备问题的GPU训练脚本
"""

import os
import sys
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import time
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from esm2_codon_optimizer import ESM2CodonOptimizer, ECOLI_CODON_TABLE
from data_processor import create_data_loaders

def setup_gpu_environment():
    """设置GPU环境"""
    # 设置CUDA环境变量
    os.environ['CUDA_HOME'] = '/usr/local/cuda-12.6'
    os.environ['CUDA_ROOT'] = '/usr/local/cuda-12.6'
    
    # 检查GPU可用性
    if torch.cuda.is_available():
        device = torch.device('cuda:0')
        print(f"✓ 使用GPU: {torch.cuda.get_device_name(0)}")
        print(f"✓ GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        
        # 设置GPU内存管理
        torch.cuda.empty_cache()
        torch.backends.cudnn.benchmark = True  # 优化cudnn性能
        
        return device
    else:
        print("❌ GPU不可用，使用CPU")
        return torch.device('cpu')

def safe_to_device(obj, device):
    """安全地将对象移动到指定设备"""
    if isinstance(obj, torch.Tensor):
        return obj.to(device)
    elif isinstance(obj, dict):
        return {k: safe_to_device(v, device) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [safe_to_device(item, device) for item in obj]
    else:
        return obj

def compute_loss_safe(predictions, protein_sequences, original_codons, device):
    """安全的损失计算，确保所有张量在正确设备上"""
    total_loss = torch.tensor(0.0, device=device, requires_grad=True)
    total_correct = 0
    total_positions = 0
    
    for b, (protein_seq, target_codons, pred_list) in enumerate(zip(protein_sequences, original_codons, predictions)):
        for pos, (aa, target_codon) in enumerate(zip(protein_seq, target_codons)):
            if aa in ECOLI_CODON_TABLE and pos < len(pred_list):
                codons = ECOLI_CODON_TABLE[aa]
                
                if len(codons) > 1 and target_codon in codons:
                    target_idx = codons.index(target_codon)
                    pred_logits = pred_list[pos]
                    
                    # 确保pred_logits在正确设备上
                    pred_logits = pred_logits.to(device)
                    
                    if target_idx < pred_logits.size(0):
                        # 创建目标张量在正确设备上
                        target_tensor = torch.tensor([target_idx], device=device, dtype=torch.long)
                        
                        # 计算交叉熵损失
                        ce_loss = nn.CrossEntropyLoss()(
                            pred_logits.unsqueeze(0),
                            target_tensor
                        )
                        total_loss = total_loss + ce_loss
                        
                        # 准确率计算
                        pred_idx = torch.argmax(pred_logits).item()
                        if pred_idx == target_idx:
                            total_correct += 1
                        total_positions += 1
    
    if total_positions > 0:
        avg_loss = total_loss / total_positions
        accuracy = total_correct / total_positions
    else:
        avg_loss = torch.tensor(0.0, device=device, requires_grad=True)
        accuracy = 0.0
    
    return avg_loss, accuracy, total_positions

def train_epoch_safe(model, train_loader, optimizer, device, epoch):
    """安全的训练epoch，处理设备问题"""
    model.train()
    total_loss = 0.0
    total_accuracy = 0.0
    total_batches = 0
    
    print(f"\nEpoch {epoch+1} 训练开始...")
    start_time = time.time()
    
    for batch_idx, batch in enumerate(train_loader):
        optimizer.zero_grad()
        
        protein_sequences = batch['protein_sequences']
        original_codons = batch['original_codons']
        masked_codons = batch['masked_codons']
        
        try:
            # 前向传播
            predictions, _ = model(protein_sequences, masked_codons)
            
            # 安全的损失计算
            loss, accuracy, positions = compute_loss_safe(
                predictions, protein_sequences, original_codons, device
            )
            
            if positions > 0 and loss.requires_grad:
                # 反向传播
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                total_loss += loss.item()
                total_accuracy += accuracy
                total_batches += 1
                
                # 打印进度
                if batch_idx % 50 == 0:
                    print(f"  Batch {batch_idx}: Loss={loss.item():.4f}, Acc={accuracy:.4f}, Pos={positions}")
                    
                    # GPU内存监控
                    if device.type == 'cuda':
                        allocated = torch.cuda.memory_allocated() / 1024**2
                        print(f"    GPU内存: {allocated:.1f} MB")
                        
                        # 定期清理GPU内存
                        if batch_idx % 100 == 0:
                            torch.cuda.empty_cache()
            else:
                print(f"  Batch {batch_idx}: 跳过（无有效位置或损失不可导）")
                
        except Exception as e:
            print(f"  Batch {batch_idx} 训练失败: {e}")
            # 清理GPU内存
            if device.type == 'cuda':
                torch.cuda.empty_cache()
            continue
    
    epoch_time = time.time() - start_time
    avg_loss = total_loss / total_batches if total_batches > 0 else 0
    avg_acc = total_accuracy / total_batches if total_batches > 0 else 0
    
    print(f"Epoch {epoch+1} 完成 ({epoch_time:.1f}s)")
    print(f"  平均损失: {avg_loss:.4f}")
    print(f"  平均准确率: {avg_acc:.4f}")
    print(f"  成功批次: {total_batches}")
    
    return avg_loss, avg_acc

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='修复设备问题的GPU训练')
    parser.add_argument('--data_file', default='processed_BL21_data_clean.csv')
    parser.add_argument('--batch_size', type=int, default=4)
    parser.add_argument('--num_epochs', type=int, default=10)
    parser.add_argument('--learning_rate', type=float, default=1e-4)
    parser.add_argument('--max_length', type=int, default=256)
    
    args = parser.parse_args()
    
    print("🚀 修复设备问题的GPU训练")
    print("=" * 50)
    
    # 设置GPU环境
    device = setup_gpu_environment()
    
    # 加载数据
    print("\n1. 加载数据...")
    train_loader, val_loader, analyzer = create_data_loaders(
        csv_file=args.data_file,
        batch_size=args.batch_size,
        max_length=args.max_length
    )
    
    # 创建模型
    print("\n2. 创建模型...")
    model = ESM2CodonOptimizer(
        esm_model_path="/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D",
        freeze_esm=True,
        fusion_dim=512
    ).to(device)
    
    print(f"模型参数: {sum(p.numel() for p in model.parameters()):,}")
    print(f"可训练参数: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    # 优化器
    optimizer = optim.AdamW(
        filter(lambda p: p.requires_grad, model.parameters()),
        lr=args.learning_rate,
        weight_decay=0.01
    )
    
    # 训练循环
    print(f"\n3. 开始训练 ({args.num_epochs} epochs)...")
    
    best_acc = 0.0
    save_dir = f"checkpoints/gpu_fixed_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(save_dir, exist_ok=True)
    
    for epoch in range(args.num_epochs):
        # 训练
        train_loss, train_acc = train_epoch_safe(
            model, train_loader, optimizer, device, epoch
        )
        
        # 保存检查点
        if train_acc > best_acc:
            best_acc = train_acc
            checkpoint_path = os.path.join(save_dir, f'best_model_epoch_{epoch+1}.pt')
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': train_loss,
                'train_accuracy': train_acc,
                'best_accuracy': best_acc
            }, checkpoint_path)
            print(f"✓ 保存最佳模型: {checkpoint_path}")
        
        # 清理GPU内存
        if device.type == 'cuda':
            torch.cuda.empty_cache()
    
    print(f"\n🎉 训练完成!")
    print(f"最佳准确率: {best_acc:.4f}")
    print(f"模型保存在: {save_dir}")

if __name__ == "__main__":
    main()
