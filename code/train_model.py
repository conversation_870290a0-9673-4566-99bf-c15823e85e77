#!/usr/bin/env python3
"""
主训练脚本：训练ESM2增强的大肠杆菌密码子优化模型
"""

import os
import sys
import argparse
import torch
import numpy as np
import random
from datetime import datetime
import json

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from esm2_codon_optimizer import ESM2CodonOptimizer
from data_processor import create_data_loaders
from trainer import CodonOptimizationTrainer
from evaluator import CodonOptimizationEvaluator

def set_seed(seed=42):
    """设置随机种子以确保可重现性"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Train ESM2-Enhanced Codon Optimizer')
    
    # 数据参数
    parser.add_argument('--data_file', type=str, default='processed_BL21_data.csv',
                       help='Path to the processed BL21 data CSV file')
    parser.add_argument('--batch_size', type=int, default=8,
                       help='Batch size for training')
    parser.add_argument('--max_length', type=int, default=512,
                       help='Maximum sequence length')
    parser.add_argument('--mask_ratio', type=float, default=0.15,
                       help='Masking ratio for STREAM strategy')
    
    # 模型参数
    parser.add_argument('--esm_model_path', type=str, 
                       default='/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D',
                       help='Path to ESM2 model')
    parser.add_argument('--freeze_esm', action='store_true', default=True,
                       help='Freeze ESM2 parameters during initial training')
    parser.add_argument('--fusion_dim', type=int, default=768,
                       help='Fusion layer dimension')
    
    # 训练参数
    parser.add_argument('--num_epochs', type=int, default=50,
                       help='Number of training epochs')
    parser.add_argument('--learning_rate', type=float, default=1e-4,
                       help='Learning rate')
    parser.add_argument('--warmup_epochs', type=int, default=2,
                       help='Number of warmup epochs')
    parser.add_argument('--patience', type=int, default=5,
                       help='Early stopping patience')
    
    # 系统参数
    parser.add_argument('--device', type=str, default='auto',
                       help='Device to use (auto, cpu, cuda)')
    parser.add_argument('--num_workers', type=int, default=2,
                       help='Number of data loader workers')
    parser.add_argument('--save_dir', type=str, default='checkpoints',
                       help='Directory to save checkpoints')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed')
    
    # 评估参数
    parser.add_argument('--evaluate_only', action='store_true',
                       help='Only run evaluation on existing model')
    parser.add_argument('--model_checkpoint', type=str, default=None,
                       help='Path to model checkpoint for evaluation')
    
    return parser.parse_args()

def get_device(device_arg):
    """获取计算设备"""
    if device_arg == 'auto':
        if torch.cuda.is_available():
            device = 'cuda'
            print(f"CUDA is available. Using GPU: {torch.cuda.get_device_name()}")
        else:
            device = 'cpu'
            print("CUDA not available. Using CPU.")
    else:
        device = device_arg
        
    return device

def save_config(args, save_dir):
    """保存配置文件"""
    config = vars(args)
    config['timestamp'] = datetime.now().isoformat()
    
    config_path = os.path.join(save_dir, 'config.json')
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
        
    print(f"Configuration saved to {config_path}")

def main():
    """主函数"""
    # 解析参数
    args = parse_arguments()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 获取设备
    device = get_device(args.device)
    
    # 创建保存目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_dir = os.path.join(args.save_dir, f"esm2_codon_optimizer_{timestamp}")
    os.makedirs(save_dir, exist_ok=True)
    
    # 保存配置
    save_config(args, save_dir)
    
    print("="*60)
    print("ESM2-Enhanced Codon Optimizer Training")
    print("="*60)
    print(f"Data file: {args.data_file}")
    print(f"Device: {device}")
    print(f"Batch size: {args.batch_size}")
    print(f"Max length: {args.max_length}")
    print(f"Mask ratio: {args.mask_ratio}")
    print(f"Save directory: {save_dir}")
    print("="*60)
    
    try:
        # 创建数据加载器
        print("\n1. Loading and preprocessing data...")
        train_loader, val_loader, analyzer = create_data_loaders(
            csv_file=args.data_file,
            batch_size=args.batch_size,
            train_ratio=0.8,
            max_length=args.max_length,
            mask_ratio=args.mask_ratio
        )
        
        # 创建模型
        print("\n2. Initializing model...")
        model = ESM2CodonOptimizer(
            esm_model_path=args.esm_model_path,
            freeze_esm=args.freeze_esm,
            fusion_dim=args.fusion_dim
        )
        
        print(f"Model created with {sum(p.numel() for p in model.parameters()):,} total parameters")
        print(f"Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
        
        if not args.evaluate_only:
            # 训练模型
            print("\n3. Starting training...")
            trainer = CodonOptimizationTrainer(
                model=model,
                train_loader=train_loader,
                val_loader=val_loader,
                analyzer=analyzer,
                device=device,
                save_dir=save_dir
            )
            
            # 开始训练
            history = trainer.train(
                num_epochs=args.num_epochs,
                learning_rate=args.learning_rate,
                warmup_epochs=args.warmup_epochs,
                patience=args.patience
            )
            
            print(f"\nTraining completed! Best validation accuracy: {trainer.best_val_accuracy:.4f}")
            
            # 加载最佳模型进行评估
            best_model_path = os.path.join(save_dir, 'best_model.pt')
            if os.path.exists(best_model_path):
                print(f"\n4. Loading best model for evaluation...")
                checkpoint = torch.load(best_model_path, map_location=device)
                model.load_state_dict(checkpoint['model_state_dict'])
                
        else:
            # 仅评估模式
            if args.model_checkpoint:
                print(f"\n3. Loading model checkpoint: {args.model_checkpoint}")
                checkpoint = torch.load(args.model_checkpoint, map_location=device)
                model.load_state_dict(checkpoint['model_state_dict'])
            else:
                print("Error: --model_checkpoint must be specified in evaluation mode")
                return
                
        # 评估模型
        print("\n4. Evaluating model...")
        evaluator = CodonOptimizationEvaluator(model, analyzer, device)
        
        # 准备评估数据（使用验证集的一部分）
        eval_protein_sequences = []
        eval_nucleotide_sequences = []
        
        # 从验证集中采样一些序列进行评估
        sample_size = min(100, len(val_loader.dataset))
        indices = np.random.choice(len(val_loader.dataset), sample_size, replace=False)
        
        for idx in indices:
            item = val_loader.dataset[idx]
            eval_protein_sequences.append(item['protein_sequence'])
            eval_nucleotide_sequences.append(''.join(item['original_codons']))
            
        print(f"Evaluating on {len(eval_protein_sequences)} sequences...")
        
        # 运行基准测试
        benchmark_results = evaluator.benchmark_against_baselines(
            eval_protein_sequences, eval_nucleotide_sequences
        )
        
        # 绘制结果
        plot_path = os.path.join(save_dir, 'evaluation_results.png')
        evaluator.plot_evaluation_results(benchmark_results, plot_path)
        
        # 保存评估结果
        results_path = os.path.join(save_dir, 'evaluation_results.json')
        
        # 转换numpy数组为列表以便JSON序列化
        json_results = {}
        for method, metrics in benchmark_results.items():
            json_results[method] = {}
            for metric, values in metrics.items():
                json_results[method][metric] = {
                    'mean': float(np.mean(values)),
                    'std': float(np.std(values)),
                    'values': [float(v) for v in values]
                }
                
        with open(results_path, 'w') as f:
            json.dump(json_results, f, indent=2)
            
        print(f"\nEvaluation results saved to {results_path}")
        print(f"Evaluation plots saved to {plot_path}")
        
        # 检查是否达到目标准确率
        our_results = benchmark_results['ESM2_CodonOptimizer']
        avg_nucleotide_accuracy = np.mean(our_results['nucleotide_accuracy'])
        
        print(f"\n{'='*60}")
        print(f"FINAL RESULTS")
        print(f"{'='*60}")
        print(f"Average nucleotide accuracy: {avg_nucleotide_accuracy:.4f}")
        print(f"Target accuracy (80%): {'✓ ACHIEVED' if avg_nucleotide_accuracy >= 0.8 else '✗ NOT ACHIEVED'}")
        print(f"{'='*60}")
        
    except Exception as e:
        print(f"\nError during training/evaluation: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
        
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
