#!/usr/bin/env python3
"""
测试设备修复是否有效
"""

import torch
import sys
import os
sys.path.append('.')

from esm2_codon_optimizer import ESM2CodonOptimizer, ECOLI_CODON_TABLE

def test_device_fix():
    """测试设备修复"""
    print("🔧 测试设备修复...")
    
    # 设置设备
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建模型
    print("创建模型...")
    model = ESM2CodonOptimizer(
        esm_model_path="/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D",
        freeze_esm=True,
        fusion_dim=256
    ).to(device)
    
    print(f"模型设备: {next(model.parameters()).device}")
    
    # 测试简单序列
    test_protein = "MKRISTTITTTITITTGNGAG"
    test_codons = ["ATG", "AAA", "CGT", "ATT", "AGC", "ACT", "ACT", "ATT", "ACT", "ACT", "ACT", "ATT", "ACT", "ATT", "ACT", "ACT", "GGT", "AAT", "GGT", "GCG", "GGT"]
    
    print(f"测试蛋白质: {test_protein}")
    print(f"测试密码子数量: {len(test_codons)}")
    
    try:
        print("\n测试前向传播...")
        
        # 前向传播
        predictions, mask = model([test_protein], [test_codons])
        
        print(f"✓ 前向传播成功")
        print(f"预测结果类型: {type(predictions)}")
        print(f"预测结果长度: {len(predictions)}")
        
        if len(predictions) > 0:
            print(f"第一个样本预测长度: {len(predictions[0])}")
            
            # 检查每个预测张量的设备
            for i, pred_tensor in enumerate(predictions[0][:5]):  # 只检查前5个
                print(f"  位置 {i} 张量设备: {pred_tensor.device}")
                print(f"  位置 {i} 张量形状: {pred_tensor.shape}")
        
        print("\n测试损失计算...")
        
        # 模拟损失计算
        total_loss = torch.tensor(0.0, device=device, requires_grad=True)
        
        for pos, (aa, target_codon) in enumerate(zip(test_protein, test_codons)):
            if aa in ECOLI_CODON_TABLE and pos < len(predictions[0]):
                codons = ECOLI_CODON_TABLE[aa]
                
                if len(codons) > 1 and target_codon in codons:
                    target_idx = codons.index(target_codon)
                    pred_logits = predictions[0][pos]
                    
                    # 确保张量在正确设备上
                    pred_logits = pred_logits.to(device)
                    target_tensor = torch.tensor([target_idx], device=device, dtype=torch.long)
                    
                    print(f"  位置 {pos}: pred_device={pred_logits.device}, target_device={target_tensor.device}")
                    
                    # 计算损失
                    ce_loss = torch.nn.CrossEntropyLoss()(
                        pred_logits.unsqueeze(0),
                        target_tensor
                    )
                    total_loss = total_loss + ce_loss
                    
                    print(f"  位置 {pos}: 损失计算成功, loss={ce_loss.item():.4f}")
                    
                    if pos >= 3:  # 只测试前几个位置
                        break
        
        print(f"\n✓ 总损失: {total_loss.item():.4f}")
        print(f"✓ 损失张量设备: {total_loss.device}")
        print(f"✓ 损失需要梯度: {total_loss.requires_grad}")
        
        # 测试反向传播
        print("\n测试反向传播...")
        total_loss.backward()
        print("✓ 反向传播成功")
        
        print("\n🎉 设备修复测试完全成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ 设备修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_device_fix()
    
    if success:
        print("\n✅ 设备问题已修复，可以开始正常训练！")
        print("\n建议使用以下命令开始训练:")
        print("python train_gpu_fixed.py --batch_size 4 --num_epochs 10 --max_length 256")
    else:
        print("\n❌ 设备问题仍然存在，需要进一步调试。")
