"""
训练模块：训练ESM2增强的密码子优化模型
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import os
import json
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple
import time
from datetime import datetime

from esm2_codon_optimizer import ESM2CodonOptimizer, CODON_TO_IDX, ALL_CODONS
from data_processor import CodonUsageAnalyzer

class CodonOptimizationTrainer:
    """密码子优化模型训练器"""
    
    def __init__(self, model: ESM2CodonOptimizer, train_loader: DataLoader, 
                 val_loader: DataLoader, analyzer: CodonUsageAnalyzer,
                 device: str = 'cpu', save_dir: str = 'checkpoints'):
        
        self.model = model.to(device)
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.analyzer = analyzer
        self.device = device
        self.save_dir = save_dir
        
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        
        # 训练历史
        self.train_history = {
            'train_loss': [],
            'val_loss': [],
            'train_accuracy': [],
            'val_accuracy': [],
            'learning_rate': []
        }
        
        # 最佳模型指标
        self.best_val_loss = float('inf')
        self.best_val_accuracy = 0.0
        
    def compute_enhanced_loss(self, predictions, targets, protein_sequences, original_codons):
        """计算增强损失函数"""
        # 主要损失：密码子分类交叉熵
        main_loss = nn.CrossEntropyLoss(ignore_index=-1)(
            predictions.view(-1, predictions.size(-1)), 
            targets.view(-1)
        )
        
        # 辅助损失1：GC含量约束
        gc_loss = self._compute_gc_content_loss(predictions, original_codons)
        
        # 辅助损失2：密码子频率分布约束
        freq_loss = self._compute_frequency_loss(predictions)
        
        # 总损失
        total_loss = main_loss + 0.1 * gc_loss + 0.05 * freq_loss
        
        return total_loss, {
            'main_loss': main_loss.item(),
            'gc_loss': gc_loss.item(),
            'freq_loss': freq_loss.item()
        }
        
    def _compute_gc_content_loss(self, predictions, original_codons):
        """计算GC含量损失"""
        batch_size = len(original_codons)
        gc_losses = []
        
        for i in range(batch_size):
            # 计算原始序列的GC含量
            original_gc = self.analyzer.calculate_gc_content(original_codons[i])
            
            # 计算预测序列的期望GC含量
            pred_probs = torch.softmax(predictions[i], dim=-1)  # [seq_len, num_codons]
            
            expected_gc = 0.0
            for j in range(pred_probs.size(0)):
                for k, codon in enumerate(ALL_CODONS):
                    gc_content = (codon.count('G') + codon.count('C')) / 3.0
                    expected_gc += pred_probs[j, k] * gc_content
                    
            expected_gc /= pred_probs.size(0)  # 平均GC含量
            
            gc_loss = (expected_gc - original_gc) ** 2
            gc_losses.append(gc_loss)
            
        return torch.mean(torch.stack(gc_losses))
        
    def _compute_frequency_loss(self, predictions):
        """计算密码子频率分布损失"""
        # 计算预测的密码子分布
        pred_probs = torch.softmax(predictions, dim=-1)  # [batch, seq_len, num_codons]
        pred_freq = torch.mean(pred_probs, dim=[0, 1])   # [num_codons]
        
        # 目标频率分布（基于训练数据）
        target_freq = torch.zeros(len(ALL_CODONS), device=self.device)
        for i, codon in enumerate(ALL_CODONS):
            target_freq[i] = self.analyzer.codon_frequencies.get(codon, 1e-6)
            
        # KL散度损失
        kl_loss = nn.KLDivLoss(reduction='batchmean')(
            torch.log(pred_freq + 1e-8), target_freq
        )
        
        return kl_loss
        
    def compute_accuracy(self, predictions, targets, mask):
        """计算准确率"""
        pred_indices = torch.argmax(predictions, dim=-1)  # [batch, seq_len]
        
        # 只计算非掩码位置的准确率
        valid_positions = mask & (targets != -1)
        
        if valid_positions.sum() == 0:
            return 0.0
            
        correct = (pred_indices == targets) & valid_positions
        accuracy = correct.sum().float() / valid_positions.sum().float()
        
        return accuracy.item()
        
    def train_epoch(self, optimizer, epoch):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        total_accuracy = 0.0
        num_batches = 0
        
        loss_components = {'main_loss': 0.0, 'gc_loss': 0.0, 'freq_loss': 0.0}
        
        pbar = tqdm(self.train_loader, desc=f'Epoch {epoch+1} Training')
        
        for batch in pbar:
            optimizer.zero_grad()
            
            # 准备数据
            protein_sequences = batch['protein_sequences']
            original_codons = batch['original_codons']
            masked_codons = batch['masked_codons']
            mask_positions = batch['mask_positions']
            
            # 创建目标张量
            targets = self._create_targets(original_codons, mask_positions)
            
            # 前向传播
            predictions, attention_mask = self.model(protein_sequences, masked_codons)
            
            # 计算损失
            loss, loss_dict = self.compute_enhanced_loss(
                predictions, targets, protein_sequences, original_codons
            )
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            optimizer.step()
            
            # 计算准确率
            accuracy = self.compute_accuracy(predictions, targets, attention_mask)
            
            # 更新统计
            total_loss += loss.item()
            total_accuracy += accuracy
            num_batches += 1
            
            for key, value in loss_dict.items():
                loss_components[key] += value
                
            # 更新进度条
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{accuracy:.4f}'
            })
            
        # 计算平均值
        avg_loss = total_loss / num_batches
        avg_accuracy = total_accuracy / num_batches
        
        for key in loss_components:
            loss_components[key] /= num_batches
            
        return avg_loss, avg_accuracy, loss_components
        
    def validate_epoch(self, epoch):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0.0
        total_accuracy = 0.0
        num_batches = 0
        
        with torch.no_grad():
            pbar = tqdm(self.val_loader, desc=f'Epoch {epoch+1} Validation')
            
            for batch in pbar:
                # 准备数据
                protein_sequences = batch['protein_sequences']
                original_codons = batch['original_codons']
                masked_codons = batch['masked_codons']
                mask_positions = batch['mask_positions']
                
                # 创建目标张量
                targets = self._create_targets(original_codons, mask_positions)
                
                # 前向传播
                predictions, attention_mask = self.model(protein_sequences, masked_codons)
                
                # 计算损失
                loss, _ = self.compute_enhanced_loss(
                    predictions, targets, protein_sequences, original_codons
                )
                
                # 计算准确率
                accuracy = self.compute_accuracy(predictions, targets, attention_mask)
                
                # 更新统计
                total_loss += loss.item()
                total_accuracy += accuracy
                num_batches += 1
                
                # 更新进度条
                pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Acc': f'{accuracy:.4f}'
                })
                
        # 计算平均值
        avg_loss = total_loss / num_batches
        avg_accuracy = total_accuracy / num_batches
        
        return avg_loss, avg_accuracy
        
    def _create_targets(self, original_codons, mask_positions):
        """创建目标张量"""
        batch_size = len(original_codons)
        max_len = max(len(codons) for codons in original_codons)
        
        targets = torch.full((batch_size, max_len), -1, dtype=torch.long, device=self.device)
        
        for i, (codons, mask_pos) in enumerate(zip(original_codons, mask_positions)):
            for pos in mask_pos:
                if pos < len(codons) and codons[pos] in CODON_TO_IDX:
                    targets[i, pos] = CODON_TO_IDX[codons[pos]]
                    
        return targets
        
    def save_checkpoint(self, epoch, optimizer, is_best=False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'train_history': self.train_history,
            'best_val_loss': self.best_val_loss,
            'best_val_accuracy': self.best_val_accuracy
        }
        
        # 保存当前检查点
        checkpoint_path = os.path.join(self.save_dir, f'checkpoint_epoch_{epoch+1}.pt')
        torch.save(checkpoint, checkpoint_path)
        
        # 保存最佳模型
        if is_best:
            best_path = os.path.join(self.save_dir, 'best_model.pt')
            torch.save(checkpoint, best_path)
            print(f"New best model saved with validation accuracy: {self.best_val_accuracy:.4f}")
            
    def plot_training_history(self):
        """绘制训练历史"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 损失曲线
        ax1.plot(self.train_history['train_loss'], label='Train Loss', color='blue')
        ax1.plot(self.train_history['val_loss'], label='Val Loss', color='red')
        ax1.set_title('Training and Validation Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)
        
        # 准确率曲线
        ax2.plot(self.train_history['train_accuracy'], label='Train Accuracy', color='blue')
        ax2.plot(self.train_history['val_accuracy'], label='Val Accuracy', color='red')
        ax2.set_title('Training and Validation Accuracy')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy')
        ax2.legend()
        ax2.grid(True)
        
        # 学习率曲线
        ax3.plot(self.train_history['learning_rate'], label='Learning Rate', color='green')
        ax3.set_title('Learning Rate Schedule')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Learning Rate')
        ax3.legend()
        ax3.grid(True)
        
        # 最佳指标显示
        ax4.text(0.1, 0.8, f'Best Val Loss: {self.best_val_loss:.4f}', fontsize=14)
        ax4.text(0.1, 0.6, f'Best Val Accuracy: {self.best_val_accuracy:.4f}', fontsize=14)
        ax4.text(0.1, 0.4, f'Total Epochs: {len(self.train_history["train_loss"])}', fontsize=14)
        ax4.set_title('Training Summary')
        ax4.axis('off')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, 'training_history.png'), dpi=300, bbox_inches='tight')
        plt.show()
        
    def train(self, num_epochs: int, learning_rate: float = 1e-4, 
              warmup_epochs: int = 2, patience: int = 5):
        """完整训练流程"""
        
        print(f"Starting training for {num_epochs} epochs...")
        print(f"Device: {self.device}")
        print(f"Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        print(f"Trainable parameters: {sum(p.numel() for p in self.model.parameters() if p.requires_grad):,}")
        
        # 优化器和调度器
        optimizer = optim.AdamW(
            filter(lambda p: p.requires_grad, self.model.parameters()),
            lr=learning_rate,
            weight_decay=0.01
        )
        
        scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer, T_0=10, T_mult=2, eta_min=1e-6
        )
        
        # 早停计数器
        patience_counter = 0
        
        # 训练循环
        for epoch in range(num_epochs):
            start_time = time.time()
            
            # 训练
            train_loss, train_acc, loss_components = self.train_epoch(optimizer, epoch)
            
            # 验证
            val_loss, val_acc = self.validate_epoch(epoch)
            
            # 更新学习率
            scheduler.step()
            current_lr = optimizer.param_groups[0]['lr']
            
            # 记录历史
            self.train_history['train_loss'].append(train_loss)
            self.train_history['val_loss'].append(val_loss)
            self.train_history['train_accuracy'].append(train_acc)
            self.train_history['val_accuracy'].append(val_acc)
            self.train_history['learning_rate'].append(current_lr)
            
            # 检查是否为最佳模型
            is_best = val_acc > self.best_val_accuracy
            if is_best:
                self.best_val_accuracy = val_acc
                self.best_val_loss = val_loss
                patience_counter = 0
            else:
                patience_counter += 1
                
            # 保存检查点
            self.save_checkpoint(epoch, optimizer, is_best)
            
            # 打印统计信息
            epoch_time = time.time() - start_time
            print(f"\nEpoch {epoch+1}/{num_epochs} ({epoch_time:.1f}s)")
            print(f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}")
            print(f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
            print(f"LR: {current_lr:.2e}")
            print(f"Loss Components - Main: {loss_components['main_loss']:.4f}, "
                  f"GC: {loss_components['gc_loss']:.4f}, Freq: {loss_components['freq_loss']:.4f}")
            
            # 早停检查
            if patience_counter >= patience:
                print(f"\nEarly stopping triggered after {patience} epochs without improvement")
                break
                
        print(f"\nTraining completed!")
        print(f"Best validation accuracy: {self.best_val_accuracy:.4f}")
        
        # 绘制训练历史
        self.plot_training_history()
        
        return self.train_history
