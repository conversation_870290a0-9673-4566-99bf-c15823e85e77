#!/usr/bin/env python3
"""
序列优化脚本：使用训练好的模型优化蛋白质序列的密码子
"""

import os
import sys
import argparse
import torch
import json
from Bio.Seq import Seq
try:
    from Bio.SeqUtils import GC
except ImportError:
    # 如果导入失败，使用自定义GC计算函数
    def GC(seq):
        """计算GC含量"""
        seq = seq.upper()
        gc_count = seq.count('G') + seq.count('C')
        return (gc_count / len(seq)) * 100 if len(seq) > 0 else 0

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from esm2_codon_optimizer import ESM2CodonOptimizer, ECOLI_CODON_TABLE
from data_processor import CodonUsageAnalyzer, EcoliCodonDataset

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Optimize protein sequence codons using ESM2-Enhanced model')
    
    parser.add_argument('--protein_sequence', type=str, required=True,
                       help='Protein sequence to optimize (single letter amino acid code)')
    parser.add_argument('--model_checkpoint', type=str, required=True,
                       help='Path to trained model checkpoint')
    parser.add_argument('--output_file', type=str, default=None,
                       help='Output file to save optimized sequence (optional)')
    parser.add_argument('--temperature', type=float, default=1.0,
                       help='Temperature for sampling (higher = more diverse)')
    parser.add_argument('--device', type=str, default='auto',
                       help='Device to use (auto, cpu, cuda)')
    parser.add_argument('--data_file', type=str, default='processed_BL21_data.csv',
                       help='Data file for codon usage analysis')
    
    return parser.parse_args()

def get_device(device_arg):
    """获取计算设备"""
    if device_arg == 'auto':
        if torch.cuda.is_available():
            device = 'cuda'
        else:
            device = 'cpu'
    else:
        device = device_arg
        
    return device

def validate_protein_sequence(sequence):
    """验证蛋白质序列"""
    valid_aa = set('ARNDCQEGHILKMFPSTWYV')
    
    # 移除空格和换行符
    sequence = sequence.replace(' ', '').replace('\n', '').upper()
    
    # 检查是否包含无效字符
    invalid_chars = set(sequence) - valid_aa
    if invalid_chars:
        raise ValueError(f"Invalid amino acids found: {invalid_chars}")
        
    if len(sequence) == 0:
        raise ValueError("Empty protein sequence")
        
    return sequence

def analyze_sequence(original_dna, optimized_dna, protein_seq):
    """分析序列特征"""
    analysis = {}
    
    # 基本信息
    analysis['protein_length'] = len(protein_seq)
    analysis['dna_length'] = len(optimized_dna)
    
    # GC含量
    if original_dna:
        analysis['original_gc_content'] = GC(original_dna) / 100.0
    analysis['optimized_gc_content'] = GC(optimized_dna) / 100.0
    
    # 验证翻译
    translated = str(Seq(optimized_dna).translate())
    analysis['translation_correct'] = translated == protein_seq
    
    if not analysis['translation_correct']:
        analysis['translation_error'] = f"Expected: {protein_seq}, Got: {translated}"
        
    # 密码子使用统计
    codons = [optimized_dna[i:i+3] for i in range(0, len(optimized_dna), 3)]
    codon_counts = {}
    for codon in codons:
        codon_counts[codon] = codon_counts.get(codon, 0) + 1
        
    analysis['codon_usage'] = codon_counts
    analysis['unique_codons'] = len(codon_counts)
    
    return analysis

def format_output(protein_seq, optimized_dna, analysis, original_dna=None):
    """格式化输出"""
    output = []
    output.append("="*60)
    output.append("ESM2-Enhanced Codon Optimization Results")
    output.append("="*60)
    
    output.append(f"\nInput protein sequence ({len(protein_seq)} aa):")
    output.append(protein_seq)
    
    if original_dna:
        output.append(f"\nOriginal DNA sequence ({len(original_dna)} bp):")
        output.append(original_dna)
        output.append(f"Original GC content: {analysis.get('original_gc_content', 0):.2%}")
    
    output.append(f"\nOptimized DNA sequence ({len(optimized_dna)} bp):")
    output.append(optimized_dna)
    output.append(f"Optimized GC content: {analysis['optimized_gc_content']:.2%}")
    
    output.append(f"\nSequence Analysis:")
    output.append(f"  Protein length: {analysis['protein_length']} amino acids")
    output.append(f"  DNA length: {analysis['dna_length']} base pairs")
    output.append(f"  Translation correct: {analysis['translation_correct']}")
    output.append(f"  Unique codons used: {analysis['unique_codons']}")
    
    if not analysis['translation_correct']:
        output.append(f"  Translation error: {analysis.get('translation_error', 'Unknown')}")
    
    output.append(f"\nCodon usage:")
    for codon, count in sorted(analysis['codon_usage'].items()):
        aa = None
        for amino_acid, codon_list in ECOLI_CODON_TABLE.items():
            if codon in codon_list:
                aa = amino_acid
                break
        output.append(f"  {codon} ({aa}): {count}")
    
    output.append("\n" + "="*60)
    
    return "\n".join(output)

def main():
    """主函数"""
    args = parse_arguments()
    
    try:
        # 验证输入
        protein_seq = validate_protein_sequence(args.protein_sequence)
        device = get_device(args.device)
        
        print("ESM2-Enhanced Codon Optimizer")
        print("="*40)
        print(f"Protein sequence: {protein_seq}")
        print(f"Model checkpoint: {args.model_checkpoint}")
        print(f"Device: {device}")
        print(f"Temperature: {args.temperature}")
        
        # 检查模型文件
        if not os.path.exists(args.model_checkpoint):
            raise FileNotFoundError(f"Model checkpoint not found: {args.model_checkpoint}")
            
        # 创建分析器（用于密码子使用分析）
        print("\nLoading codon usage analyzer...")
        if os.path.exists(args.data_file):
            dataset = EcoliCodonDataset(args.data_file, max_length=1024)
            analyzer = CodonUsageAnalyzer(dataset)
            analyzer.analyze_codon_usage()
        else:
            print(f"Warning: Data file {args.data_file} not found. Using default analyzer.")
            analyzer = None
            
        # 加载模型
        print("Loading model...")
        model = ESM2CodonOptimizer(
            esm_model_path="/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D",
            freeze_esm=True,
            fusion_dim=768
        )
        
        # 加载检查点
        checkpoint = torch.load(args.model_checkpoint, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(device)
        
        print(f"Model loaded successfully from epoch {checkpoint.get('epoch', 'unknown')}")
        
        # 优化序列
        print("Optimizing codon usage...")
        optimized_codons = model.predict_optimal_codons(protein_seq, temperature=args.temperature)
        optimized_dna = ''.join(optimized_codons)
        
        # 分析结果
        analysis = analyze_sequence(None, optimized_dna, protein_seq)
        
        # 格式化输出
        result_text = format_output(protein_seq, optimized_dna, analysis)
        
        # 打印结果
        print("\n" + result_text)
        
        # 保存到文件
        if args.output_file:
            with open(args.output_file, 'w') as f:
                f.write(result_text)
                f.write(f"\n\n# FASTA format\n")
                f.write(f">Optimized_sequence\n")
                f.write(f"{optimized_dna}\n")
                
            print(f"\nResults saved to: {args.output_file}")
            
        # 保存JSON格式的详细结果
        if args.output_file:
            json_file = args.output_file.replace('.txt', '.json')
            json_data = {
                'input_protein': protein_seq,
                'optimized_dna': optimized_dna,
                'optimized_codons': optimized_codons,
                'analysis': analysis,
                'parameters': {
                    'temperature': args.temperature,
                    'model_checkpoint': args.model_checkpoint
                }
            }
            
            with open(json_file, 'w') as f:
                json.dump(json_data, f, indent=2)
                
            print(f"Detailed results saved to: {json_file}")
            
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
        
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
