#!/usr/bin/env python3
"""
ESM2增强的密码子优化工具
使用训练好的最佳模型进行蛋白质序列的密码子优化
"""

import torch
import torch.nn.functional as F
import argparse
import sys
import os
from Bio.Seq import Seq
from Bio import SeqIO
from Bio.SeqRecord import SeqRecord
import pandas as pd
from datetime import datetime
import json

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from esm2_codon_optimizer import ESM2CodonOptimizer, ECOLI_CODON_TABLE
from data_processor import CodonUsageAnalyzer

class SequenceOptimizer:
    """序列优化器"""
    
    def __init__(self, model_path, device='auto'):
        """
        初始化优化器
        
        Args:
            model_path: 训练好的模型路径
            device: 计算设备 ('auto', 'cpu', 'cuda')
        """
        self.device = self._setup_device(device)
        self.model = self._load_model(model_path)
        # 创建一个简化的分析器，不依赖数据集
        self.analyzer = None
        
    def _setup_device(self, device):
        """设置计算设备"""
        if device == 'auto':
            if torch.cuda.is_available():
                device = torch.device('cuda:0')
                print(f"✓ 使用GPU: {torch.cuda.get_device_name(0)}")
            else:
                device = torch.device('cpu')
                print("✓ 使用CPU")
        else:
            device = torch.device(device)
            print(f"✓ 使用指定设备: {device}")
            
        return device
    
    def _load_model(self, model_path):
        """加载训练好的模型"""
        print(f"加载模型: {model_path}")
        
        # 创建模型实例
        model = ESM2CodonOptimizer(
            esm_model_path="/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D/snapshots/255893e6608ab942fb16da47f62667c303c571d6",
            freeze_esm=True,
            fusion_dim=512
        )
        
        # 加载训练好的权重
        checkpoint = torch.load(model_path, map_location=self.device)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(self.device)
        model.eval()
        
        print(f"✓ 模型加载成功")
        if 'train_accuracy' in checkpoint:
            print(f"✓ 模型训练准确率: {checkpoint['train_accuracy']:.4f}")
        if 'epoch' in checkpoint:
            print(f"✓ 训练轮数: {checkpoint['epoch'] + 1}")
            
        return model
    
    def validate_protein_sequence(self, sequence):
        """验证蛋白质序列"""
        sequence = sequence.upper().strip()
        
        # 移除空格和换行符
        sequence = ''.join(sequence.split())
        
        # 检查是否只包含标准氨基酸
        valid_aa = set('ARNDCQEGHILKMFPSTWYV')
        invalid_chars = set(sequence) - valid_aa
        
        if invalid_chars:
            raise ValueError(f"序列包含无效字符: {invalid_chars}")
            
        if len(sequence) < 1:
            raise ValueError("序列长度不能为空")
            
        return sequence
    
    def optimize_sequence(self, protein_sequence, temperature=1.0, return_stats=True):
        """
        优化蛋白质序列的密码子使用
        
        Args:
            protein_sequence: 蛋白质序列
            temperature: 温度参数，控制随机性 (默认1.0)
            return_stats: 是否返回统计信息
            
        Returns:
            dict: 包含优化结果和统计信息
        """
        # 验证输入序列
        protein_sequence = self.validate_protein_sequence(protein_sequence)
        
        print(f"优化蛋白质序列 (长度: {len(protein_sequence)})")
        print(f"序列: {protein_sequence}")
        
        # 使用模型进行密码子优化
        with torch.no_grad():
            optimal_codons = self.model.predict_optimal_codons(
                protein_sequence, 
                temperature=temperature
            )
        
        # 构建优化后的DNA序列
        optimized_dna = ''.join(optimal_codons)
        
        # 验证翻译正确性
        translated = str(Seq(optimized_dna).translate())
        if translated != protein_sequence:
            raise RuntimeError("优化后序列翻译不匹配原始蛋白质序列")
        
        result = {
            'original_protein': protein_sequence,
            'optimized_codons': optimal_codons,
            'optimized_dna': optimized_dna,
            'translation_verified': True
        }
        
        if return_stats:
            stats = self._calculate_statistics(protein_sequence, optimal_codons)
            result.update(stats)
            
        return result
    
    def _calculate_statistics(self, protein_sequence, codons):
        """计算优化统计信息"""
        dna_sequence = ''.join(codons)
        
        # GC含量
        gc_content = (dna_sequence.count('G') + dna_sequence.count('C')) / len(dna_sequence)
        
        # 密码子使用偏好分析
        codon_usage = {}
        for aa in set(protein_sequence):
            if aa in ECOLI_CODON_TABLE:
                aa_codons = [codon for codon, aa_code in zip(codons, protein_sequence) if aa_code == aa]
                if aa_codons:
                    codon_counts = {codon: aa_codons.count(codon) for codon in set(aa_codons)}
                    codon_usage[aa] = codon_counts
        
        # CAI计算 (简化版)
        cai_score = self._calculate_cai(codons)
        
        return {
            'gc_content': gc_content,
            'codon_usage': codon_usage,
            'cai_score': cai_score,
            'sequence_length': len(protein_sequence),
            'dna_length': len(dna_sequence)
        }
    
    def _calculate_cai(self, codons):
        """计算密码子适应指数 (简化版)"""
        # 使用简化的CAI计算，基于E.coli密码子频率
        # 这里使用预定义的频率数据
        ecoli_frequencies = {
            'TTT': 0.58, 'TTC': 0.42, 'TTA': 0.14, 'TTG': 0.13,
            'TCT': 0.17, 'TCC': 0.15, 'TCA': 0.14, 'TCG': 0.14,
            'TAT': 0.59, 'TAC': 0.41, 'TAA': 0.61, 'TAG': 0.09,
            'TGT': 0.46, 'TGC': 0.54, 'TGA': 0.30, 'TGG': 1.00,
            'CTT': 0.12, 'CTC': 0.10, 'CTA': 0.04, 'CTG': 0.47,
            'CCT': 0.18, 'CCC': 0.13, 'CCA': 0.20, 'CCG': 0.49,
            'CAT': 0.57, 'CAC': 0.43, 'CAA': 0.34, 'CAG': 0.66,
            'CGT': 0.36, 'CGC': 0.36, 'CGA': 0.07, 'CGG': 0.11,
            'ATT': 0.49, 'ATC': 0.39, 'ATA': 0.11, 'ATG': 1.00,
            'ACT': 0.19, 'ACC': 0.40, 'ACA': 0.17, 'ACG': 0.25,
            'AAT': 0.49, 'AAC': 0.51, 'AAA': 0.74, 'AAG': 0.26,
            'AGT': 0.16, 'AGC': 0.25, 'AGA': 0.07, 'AGG': 0.04,
            'GTT': 0.28, 'GTC': 0.20, 'GTA': 0.17, 'GTG': 0.35,
            'GCT': 0.18, 'GCC': 0.26, 'GCA': 0.23, 'GCG': 0.33,
            'GAT': 0.63, 'GAC': 0.37, 'GAA': 0.68, 'GAG': 0.32,
            'GGT': 0.35, 'GGC': 0.37, 'GGA': 0.13, 'GGG': 0.15
        }

        total_score = 0
        valid_codons = 0

        for codon in codons:
            if codon in ecoli_frequencies:
                freq = ecoli_frequencies[codon]
                total_score += freq
                valid_codons += 1

        return total_score / valid_codons if valid_codons > 0 else 0.0

def print_results(result):
    """打印优化结果"""
    print("\n" + "="*60)
    print("🧬 密码子优化结果")
    print("="*60)
    
    print(f"原始蛋白质序列: {result['original_protein']}")
    print(f"序列长度: {result['sequence_length']} 个氨基酸")
    print(f"优化后DNA序列: {result['optimized_dna']}")
    print(f"DNA长度: {result['dna_length']} 个核苷酸")
    print(f"翻译验证: {'✓ 通过' if result['translation_verified'] else '✗ 失败'}")
    
    if 'gc_content' in result:
        print(f"\n📊 序列统计:")
        print(f"GC含量: {result['gc_content']:.2%}")
        print(f"CAI评分: {result['cai_score']:.4f}")
        
        print(f"\n🔬 密码子使用分析:")
        for aa, usage in result['codon_usage'].items():
            if len(usage) > 1:  # 只显示有多个密码子选择的氨基酸
                total = sum(usage.values())
                usage_str = ", ".join([f"{codon}:{count}({count/total:.1%})" for codon, count in usage.items()])
                print(f"  {aa}: {usage_str}")

def save_results(result, output_file):
    """保存结果到文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    if output_file.endswith('.fasta') or output_file.endswith('.fa'):
        # 保存为FASTA格式
        protein_record = SeqRecord(
            Seq(result['original_protein']),
            id="original_protein",
            description=f"Original protein sequence (length: {result['sequence_length']})"
        )
        
        dna_record = SeqRecord(
            Seq(result['optimized_dna']),
            id="optimized_dna",
            description=f"Codon optimized DNA sequence (GC: {result.get('gc_content', 0):.2%}, CAI: {result.get('cai_score', 0):.4f})"
        )
        
        with open(output_file, 'w') as f:
            SeqIO.write([protein_record, dna_record], f, "fasta")
            
    elif output_file.endswith('.csv'):
        # 保存为CSV格式
        df_data = {
            'timestamp': [timestamp],
            'original_protein': [result['original_protein']],
            'optimized_dna': [result['optimized_dna']],
            'sequence_length': [result['sequence_length']],
            'gc_content': [result.get('gc_content', 0)],
            'cai_score': [result.get('cai_score', 0)],
            'translation_verified': [result['translation_verified']]
        }
        
        df = pd.DataFrame(df_data)
        df.to_csv(output_file, index=False)
        
    else:
        # 保存为文本格式
        with open(output_file, 'w') as f:
            f.write(f"密码子优化结果 - {timestamp}\n")
            f.write("="*60 + "\n\n")
            f.write(f"原始蛋白质序列:\n{result['original_protein']}\n\n")
            f.write(f"优化后DNA序列:\n{result['optimized_dna']}\n\n")
            f.write(f"序列长度: {result['sequence_length']} 个氨基酸\n")
            f.write(f"DNA长度: {result['dna_length']} 个核苷酸\n")
            f.write(f"GC含量: {result.get('gc_content', 0):.2%}\n")
            f.write(f"CAI评分: {result.get('cai_score', 0):.4f}\n")
            f.write(f"翻译验证: {'通过' if result['translation_verified'] else '失败'}\n")
    
    print(f"✓ 结果已保存到: {output_file}")

def interactive_mode():
    """交互式模式"""
    print("🧬 ESM2增强的大肠杆菌密码子优化工具 - 交互模式")
    print("="*60)
    
    # 获取输入序列
    print("\n请输入蛋白质序列 (支持多行输入，输入空行结束):")
    lines = []
    while True:
        line = input().strip()
        if not line:
            break
        lines.append(line)
    protein_sequence = ''.join(lines)
    
    if not protein_sequence:
        print("❌ 未输入序列")
        return
    
    # 选择模型
    print("\n选择模型:")
    print("1. 最新模型 (epoch 99, 推荐)")
    print("2. 早期模型 (epoch 9)")
    print("3. 自定义路径")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == "1":
        model_path = "checkpoints/gpu_fixed_20250629_143248/best_model_epoch_99.pt"
    elif choice == "2":
        model_path = "checkpoints/gpu_fixed_20250629_140048/best_model_epoch_9.pt"
    elif choice == "3":
        model_path = input("请输入模型路径: ").strip()
    else:
        model_path = "checkpoints/gpu_fixed_20250629_143248/best_model_epoch_99.pt"
        print("使用默认模型")
    
    # 优化参数
    temperature = input("温度参数 (默认1.0): ").strip()
    temperature = float(temperature) if temperature else 1.0
    
    # 输出文件
    output_file = input("输出文件路径 (可选，支持.txt/.csv/.fasta): ").strip()
    output_file = output_file if output_file else None
    
    try:
        # 初始化优化器
        optimizer = SequenceOptimizer(model_path, device='auto')
        
        # 执行优化
        result = optimizer.optimize_sequence(
            protein_sequence,
            temperature=temperature,
            return_stats=True
        )
        
        # 显示结果
        print_results(result)
        
        # 保存结果
        if output_file:
            save_results(result, output_file)
        
        print(f"\n🎉 密码子优化完成!")
        
    except Exception as e:
        print(f"\n❌ 错误: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ESM2增强的大肠杆菌密码子优化工具')
    
    # 输入选项
    input_group = parser.add_mutually_exclusive_group(required=False)
    input_group.add_argument('--sequence', '-s', type=str, 
                           help='直接输入蛋白质序列')
    input_group.add_argument('--file', '-f', type=str,
                           help='从FASTA文件读取蛋白质序列')
    input_group.add_argument('--interactive', '-i', action='store_true',
                           help='交互式输入模式')
    
    # 模型选项
    parser.add_argument('--model', '-m', type=str,
                       default='checkpoints/gpu_fixed_20250629_143248/best_model_epoch_99.pt',
                       help='训练好的模型路径 (默认: 最新最佳模型)')
    
    # 输出选项
    parser.add_argument('--output', '-o', type=str,
                       help='输出文件路径 (支持.txt, .csv, .fasta格式)')
    
    # 优化参数
    parser.add_argument('--temperature', '-t', type=float, default=1.0,
                       help='温度参数，控制随机性 (默认: 1.0)')
    parser.add_argument('--device', '-d', type=str, default='auto',
                       choices=['auto', 'cpu', 'cuda'],
                       help='计算设备 (默认: auto)')
    
    # 其他选项
    parser.add_argument('--no-stats', action='store_true',
                       help='不计算统计信息')
    
    args = parser.parse_args()
    
    # 如果没有提供任何输入参数，启动交互模式
    if not any([args.sequence, args.file, args.interactive]):
        interactive_mode()
        return
    
    print("🧬 ESM2增强的大肠杆菌密码子优化工具")
    print("="*60)
    
    try:
        # 初始化优化器
        optimizer = SequenceOptimizer(args.model, args.device)
        
        # 获取输入序列
        if args.sequence:
            protein_sequence = args.sequence
        elif args.file:
            # 从FASTA文件读取
            with open(args.file, 'r') as f:
                records = list(SeqIO.parse(f, "fasta"))
                if not records:
                    raise ValueError("FASTA文件中没有找到序列")
                protein_sequence = str(records[0].seq)
                print(f"从文件读取序列: {args.file}")
        elif args.interactive:
            # 交互式输入
            interactive_mode()
            return
        
        # 执行优化
        result = optimizer.optimize_sequence(
            protein_sequence,
            temperature=args.temperature,
            return_stats=not args.no_stats
        )
        
        # 显示结果
        print_results(result)
        
        # 保存结果
        if args.output:
            save_results(result, args.output)
        
        print(f"\n🎉 密码子优化完成!")
        
    except Exception as e:
        print(f"\n❌ 错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
