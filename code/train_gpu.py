#!/usr/bin/env python3
"""
GPU优化的训练脚本：专门为GPU训练设计的简化版本
"""

import os
import sys
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import time
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from esm2_codon_optimizer import ESM2CodonOptimizer, ECOLI_CODON_TABLE
from data_processor import create_data_loaders

def setup_gpu_environment():
    """设置GPU环境"""
    # 设置CUDA环境变量
    os.environ['CUDA_HOME'] = '/usr/local/cuda-12.6'
    os.environ['CUDA_ROOT'] = '/usr/local/cuda-12.6'
    
    # 检查GPU可用性
    if torch.cuda.is_available():
        device = torch.device('cuda:0')
        print(f"✓ 使用GPU: {torch.cuda.get_device_name(0)}")
        print(f"✓ GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        
        # 设置GPU内存管理
        torch.cuda.empty_cache()
        torch.backends.cudnn.benchmark = True  # 优化cudnn性能
        
        return device
    else:
        print("❌ GPU不可用，使用CPU")
        return torch.device('cpu')

def ensure_device(tensor_or_list, device):
    """确保张量或张量列表在正确设备上"""
    if isinstance(tensor_or_list, torch.Tensor):
        return tensor_or_list.to(device)
    elif isinstance(tensor_or_list, list):
        return [ensure_device(item, device) for item in tensor_or_list]
    else:
        return tensor_or_list

def simple_train_epoch(model, train_loader, optimizer, device, epoch):
    """简化的训练epoch"""
    model.train()
    total_loss = 0.0
    total_correct = 0
    total_positions = 0
    
    print(f"\nEpoch {epoch+1} 训练开始...")
    start_time = time.time()
    
    for batch_idx, batch in enumerate(train_loader):
        optimizer.zero_grad()
        
        protein_sequences = batch['protein_sequences']
        original_codons = batch['original_codons']
        masked_codons = batch['masked_codons']
        
        try:
            # 确保所有输入都在正确设备上
            # 注意：protein_sequences和codon_sequences是字符串列表，不需要移动设备

            # 前向传播
            predictions, _ = model(protein_sequences, masked_codons)
            
            # 简化的损失计算
            loss = 0.0
            correct = 0
            positions = 0
            
            for b, (protein_seq, target_codons) in enumerate(zip(protein_sequences, original_codons)):
                if b < len(predictions):
                    pred_list = predictions[b]
                    
                    for pos, (aa, target_codon) in enumerate(zip(protein_seq, target_codons)):
                        if aa in ECOLI_CODON_TABLE and pos < len(pred_list):
                            codons = ECOLI_CODON_TABLE[aa]
                            
                            if len(codons) > 1 and target_codon in codons:
                                target_idx = codons.index(target_codon)
                                pred_logits = pred_list[pos]
                                
                                if target_idx < pred_logits.size(0):
                                    # 确保pred_logits在正确设备上
                                    pred_logits = pred_logits.to(device)

                                    # 交叉熵损失
                                    ce_loss = nn.CrossEntropyLoss()(
                                        pred_logits.unsqueeze(0),
                                        torch.tensor([target_idx], device=device)
                                    )
                                    loss += ce_loss
                                    
                                    # 准确率
                                    pred_idx = torch.argmax(pred_logits).item()
                                    if pred_idx == target_idx:
                                        correct += 1
                                    positions += 1
            
            if positions > 0:
                loss = loss / positions
                
                # 反向传播
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                total_loss += loss.item()
                total_correct += correct
                total_positions += positions
                
                # 打印进度
                if batch_idx % 10 == 0:
                    current_acc = correct / positions if positions > 0 else 0
                    print(f"  Batch {batch_idx}: Loss={loss.item():.4f}, Acc={current_acc:.4f}")
                    
                    # GPU内存监控
                    if device.type == 'cuda':
                        allocated = torch.cuda.memory_allocated() / 1024**2
                        print(f"    GPU内存: {allocated:.1f} MB")
            
        except Exception as e:
            print(f"  Batch {batch_idx} 训练失败: {e}")
            continue
    
    epoch_time = time.time() - start_time
    avg_loss = total_loss / len(train_loader) if len(train_loader) > 0 else 0
    avg_acc = total_correct / total_positions if total_positions > 0 else 0
    
    print(f"Epoch {epoch+1} 完成 ({epoch_time:.1f}s)")
    print(f"  平均损失: {avg_loss:.4f}")
    print(f"  平均准确率: {avg_acc:.4f}")
    print(f"  总预测位置: {total_positions}")
    
    return avg_loss, avg_acc

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='GPU优化的密码子优化模型训练')
    parser.add_argument('--data_file', default='processed_BL21_data_clean.csv')
    parser.add_argument('--batch_size', type=int, default=4)
    parser.add_argument('--num_epochs', type=int, default=10)
    parser.add_argument('--learning_rate', type=float, default=1e-4)
    parser.add_argument('--max_length', type=int, default=256)
    
    args = parser.parse_args()
    
    print("🚀 GPU优化的密码子优化模型训练")
    print("=" * 50)
    
    # 设置GPU环境
    device = setup_gpu_environment()
    
    # 加载数据
    print("\n1. 加载数据...")
    train_loader, val_loader, analyzer = create_data_loaders(
        csv_file=args.data_file,
        batch_size=args.batch_size,
        max_length=args.max_length
    )
    
    # 创建模型
    print("\n2. 创建模型...")
    model = ESM2CodonOptimizer(
        esm_model_path="/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D",
        freeze_esm=True,
        fusion_dim=512  # 减小维度以节省GPU内存
    ).to(device)
    
    print(f"模型参数: {sum(p.numel() for p in model.parameters()):,}")
    print(f"可训练参数: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    # 优化器
    optimizer = optim.AdamW(
        filter(lambda p: p.requires_grad, model.parameters()),
        lr=args.learning_rate,
        weight_decay=0.01
    )
    
    # 训练循环
    print(f"\n3. 开始训练 ({args.num_epochs} epochs)...")
    
    best_acc = 0.0
    save_dir = f"checkpoints/gpu_training_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(save_dir, exist_ok=True)
    
    for epoch in range(args.num_epochs):
        # 训练
        train_loss, train_acc = simple_train_epoch(
            model, train_loader, optimizer, device, epoch
        )
        
        # 保存检查点
        if train_acc > best_acc:
            best_acc = train_acc
            checkpoint_path = os.path.join(save_dir, f'best_model_epoch_{epoch+1}.pt')
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': train_loss,
                'train_accuracy': train_acc,
                'best_accuracy': best_acc
            }, checkpoint_path)
            print(f"✓ 保存最佳模型: {checkpoint_path}")
        
        # 每个epoch都保存
        epoch_checkpoint = os.path.join(save_dir, f'checkpoint_epoch_{epoch+1}.pt')
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'train_loss': train_loss,
            'train_accuracy': train_acc
        }, epoch_checkpoint)
        
        print(f"✓ 保存epoch检查点: {epoch_checkpoint}")
        
        # 清理GPU内存
        if device.type == 'cuda':
            torch.cuda.empty_cache()
    
    print(f"\n🎉 训练完成!")
    print(f"最佳准确率: {best_acc:.4f}")
    print(f"模型保存在: {save_dir}")
    
    # 最终GPU内存状态
    if device.type == 'cuda':
        print(f"\n最终GPU内存使用:")
        print(f"  已分配: {torch.cuda.memory_allocated() / 1024**2:.1f} MB")
        print(f"  缓存: {torch.cuda.memory_reserved() / 1024**2:.1f} MB")

if __name__ == "__main__":
    main()
