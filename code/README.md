# ESM2-Enhanced Codon Optimizer for E. coli

这是一个整合了ESM2蛋白质大模型特征的大肠杆菌密码子优化系统，基于CodonTransformer的STREAM策略设计。

## 🚀 核心特性

- **ESM2蛋白质特征融合**: 利用Facebook ESM2模型提取深层蛋白质语义特征
- **跨模态注意力机制**: 创新的蛋白质-密码子特征融合架构
- **STREAM训练策略**: 借鉴CodonTransformer的掩码语言建模方法
- **大肠杆菌特异性**: 专门针对E. coli BL21进行优化
- **多重损失函数**: 结合密码子预测、GC含量和频率分布约束
- **全面评估体系**: 与多种基线方法对比评估

## 📁 项目结构

```
code/
├── esm2_codon_optimizer.py    # 核心模型架构
├── data_processor.py          # 数据处理模块
├── trainer.py                 # 训练模块
├── evaluator.py              # 评估模块
├── train_model.py            # 主训练脚本
├── optimize_sequence.py      # 序列优化脚本
├── requirements.txt          # 依赖包列表
├── processed_BL21_data.csv   # 大肠杆菌BL21数据
└── README.md                 # 说明文档
```

## 🛠️ 环境配置

### 1. 创建Conda环境

```bash
conda create -n codon python=3.9 -y
conda activate codon
```

### 2. 安装依赖

```bash
# 安装CPU版本PyTorch（推荐用于此项目）
pip install -f https://download.pytorch.org/whl/torch_stable.html torch==2.0.1+cpu torchvision==0.15.2+cpu torchaudio==2.0.2+cpu

# 安装其他依赖
pip install transformers pandas scikit-learn matplotlib seaborn tqdm biopython fair-esm
```

### 3. 验证ESM2模型路径

确保ESM2模型已下载到指定路径：
```
/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D
```

## 🎯 使用方法

### 训练模型

```bash
cd code
python train_model.py \
    --data_file processed_BL21_data.csv \
    --batch_size 8 \
    --num_epochs 50 \
    --learning_rate 1e-4 \
    --max_length 512 \
    --mask_ratio 0.15 \
    --device cpu \
    --save_dir checkpoints
```

### 主要训练参数

- `--data_file`: 训练数据文件路径
- `--batch_size`: 批次大小（建议8-16）
- `--num_epochs`: 训练轮数
- `--learning_rate`: 学习率
- `--max_length`: 最大序列长度
- `--mask_ratio`: STREAM掩码比例
- `--device`: 计算设备（cpu/cuda/auto）
- `--freeze_esm`: 是否冻结ESM2参数（默认True）

### 优化蛋白质序列

训练完成后，使用模型优化新的蛋白质序列：

```bash
python optimize_sequence.py \
    --protein_sequence "MKRISTTITTTITITTGNGAG" \
    --model_checkpoint checkpoints/esm2_codon_optimizer_20241229_123456/best_model.pt \
    --output_file optimized_result.txt \
    --temperature 1.0
```

### 仅评估模式

如果只想评估已训练的模型：

```bash
python train_model.py \
    --evaluate_only \
    --model_checkpoint checkpoints/best_model.pt \
    --data_file processed_BL21_data.csv
```

## 📊 模型架构

### 核心组件

1. **ESM2蛋白质编码器**
   - 使用预训练的ESM2-650M模型
   - 提取1280维蛋白质特征
   - 初期训练时冻结参数

2. **密码子序列编码器**
   - 512维密码子嵌入
   - 6层Transformer编码器
   - 支持64个大肠杆菌密码子

3. **跨模态融合层**
   - 双向交叉注意力机制
   - 蛋白质特征→密码子特征
   - 密码子特征→蛋白质特征
   - 残差连接和层归一化

4. **密码子预测头**
   - 多层感知机架构
   - 输出64个密码子的概率分布
   - 支持温度缩放采样

### 训练策略

1. **STREAM掩码策略**
   - 随机掩盖15%的密码子位置
   - 模型学习根据上下文预测最优密码子
   - 类似BERT的掩码语言建模

2. **多阶段训练**
   - 阶段1：冻结ESM2，训练融合层
   - 阶段2：解冻ESM2最后几层，端到端微调
   - 自适应学习率调度

3. **增强损失函数**
   - 主损失：密码子分类交叉熵
   - 辅助损失：GC含量约束
   - 正则化：密码子频率分布约束

## 📈 评估指标

### 准确率指标
- **密码子准确率**: 预测密码子与真实密码子的匹配度
- **核苷酸准确率**: 核苷酸级别的准确率（目标≥80%）
- **蛋白质准确率**: 翻译后蛋白质序列的正确性

### 生物学指标
- **GC含量相似性**: 与天然序列GC含量的接近程度
- **CAI分数**: 密码子适应指数
- **密码子频率分布**: 与大肠杆菌天然分布的相关性

### 基线对比
- **高频选择(HFC)**: 总是选择最高频率的密码子
- **随机选择**: 随机选择同义密码子
- **背景频率选择(BFC)**: 根据背景频率概率选择

## 🔧 故障排除

### 常见问题

1. **内存不足**
   ```bash
   # 减小批次大小
   --batch_size 4
   
   # 减小最大序列长度
   --max_length 256
   ```

2. **ESM2模型加载失败**
   ```bash
   # 检查模型路径
   ls /home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D
   
   # 重新下载模型
   python -c "from transformers import EsmModel; EsmModel.from_pretrained('facebook/esm2_t33_650M_UR50D')"
   ```

3. **训练速度慢**
   ```bash
   # 使用更少的数据加载器工作进程
   --num_workers 1
   
   # 冻结ESM2参数
   --freeze_esm
   ```

### 性能优化

1. **使用GPU加速**（如果可用）
   ```bash
   --device cuda
   ```

2. **调整批次大小**
   - CPU: 建议4-8
   - GPU: 可以尝试16-32

3. **序列长度限制**
   - 大多数蛋白质<512氨基酸
   - 可根据数据分布调整

## 📝 输出文件

### 训练输出
- `checkpoints/`: 模型检查点目录
- `best_model.pt`: 最佳模型
- `config.json`: 训练配置
- `training_history.png`: 训练曲线图
- `evaluation_results.json`: 评估结果

### 优化输出
- `optimized_result.txt`: 文本格式结果
- `optimized_result.json`: JSON格式详细结果
- 包含FASTA格式的优化序列

## 🎯 目标达成

项目目标是实现**80%的核苷酸准确率**。通过以下策略提升性能：

1. **数据质量**: 使用高质量的大肠杆菌BL21数据
2. **模型架构**: ESM2特征+跨模态融合
3. **训练策略**: STREAM掩码+多阶段训练
4. **损失函数**: 多重约束优化
5. **评估体系**: 全面的性能评估

## 📚 参考文献

1. CodonTransformer: The Complete Toolkit for Codon Optimization
2. ESM-2: Language models of protein sequences at the scale of evolution
3. Attention Is All You Need
4. BERT: Pre-training of Deep Bidirectional Transformers

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目采用MIT许可证。
