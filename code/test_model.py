#!/usr/bin/env python3
"""
测试模型脚本：验证模型是否正常工作
"""

import torch
import sys
import os
sys.path.append('.')

from esm2_codon_optimizer import ESM2CodonOptimizer, ECOLI_CODON_TABLE
from data_processor import EcoliCodonDataset

def test_model():
    """测试模型基本功能"""
    print("测试ESM2增强的密码子优化模型...")
    
    # 创建模型
    print("1. 创建模型...")
    model = ESM2CodonOptimizer(
        esm_model_path="/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D",
        freeze_esm=True,
        fusion_dim=768
    )
    
    print(f"模型参数总数: {sum(p.numel() for p in model.parameters()):,}")
    print(f"可训练参数: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    # 测试简单序列
    print("\n2. 测试简单序列...")
    test_protein = "MKRISTTITTTITITTGNGAG"
    test_codons = ["ATG", "AAA", "CGT", "ATT", "AGC", "ACT", "ACT", "ATT", "ACT", "ACT", "ACT", "ATT", "ACT", "ATT", "ACT", "ACT", "GGT", "AAT", "GGT", "GCG", "GGT"]
    
    print(f"测试蛋白质: {test_protein}")
    print(f"测试密码子: {test_codons}")
    
    try:
        # 前向传播测试
        print("\n3. 测试前向传播...")
        predictions, mask = model([test_protein], [test_codons])
        print(f"预测结果类型: {type(predictions)}")
        print(f"预测结果长度: {len(predictions)}")
        if len(predictions) > 0:
            print(f"第一个样本预测长度: {len(predictions[0])}")
            
        # 测试密码子预测
        print("\n4. 测试密码子预测...")
        optimal_codons = model.predict_optimal_codons(test_protein)
        print(f"优化后的密码子: {optimal_codons}")
        print(f"优化后的DNA序列: {''.join(optimal_codons)}")
        
        # 验证翻译
        from Bio.Seq import Seq
        translated = str(Seq(''.join(optimal_codons)).translate())
        print(f"翻译结果: {translated}")
        print(f"翻译正确: {translated == test_protein}")
        
        print("\n✓ 模型测试成功！")
        return True
        
    except Exception as e:
        print(f"\n✗ 模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_loading():
    """测试数据加载"""
    print("\n测试数据加载...")
    
    try:
        dataset = EcoliCodonDataset("processed_BL21_data_clean.csv", max_length=128)
        print(f"数据集大小: {len(dataset)}")
        
        # 测试第一个样本
        sample = dataset[0]
        print(f"样本蛋白质长度: {len(sample['protein_sequence'])}")
        print(f"样本密码子长度: {len(sample['original_codons'])}")
        print(f"样本蛋白质: {sample['protein_sequence'][:20]}...")
        
        print("✓ 数据加载测试成功！")
        return True
        
    except Exception as e:
        print(f"✗ 数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_codon_table():
    """测试密码子表"""
    print("\n测试密码子表...")
    
    print(f"密码子表包含 {len(ECOLI_CODON_TABLE)} 个氨基酸")
    
    # 检查每个氨基酸的密码子数量
    for aa, codons in ECOLI_CODON_TABLE.items():
        print(f"{aa}: {len(codons)} 个密码子 - {codons}")
        
    # 统计有多个密码子的氨基酸
    multi_codon_aa = [aa for aa, codons in ECOLI_CODON_TABLE.items() if len(codons) > 1 and aa != '*']
    print(f"\n有多个密码子的氨基酸: {len(multi_codon_aa)} 个")
    print(f"它们是: {multi_codon_aa}")
    
    print("✓ 密码子表测试成功！")
    return True

if __name__ == "__main__":
    print("="*60)
    print("ESM2-CodonOptimizer 模型测试")
    print("="*60)
    
    # 运行所有测试
    tests = [
        ("密码子表测试", test_codon_table),
        ("数据加载测试", test_data_loading),
        ("模型功能测试", test_model),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("测试总结:")
    print("="*60)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！模型可以开始训练。")
    else:
        print("⚠️  有测试失败，请检查问题后再训练。")
