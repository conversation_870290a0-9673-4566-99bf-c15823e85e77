"""
评估模块：评估密码子优化模型的性能
"""

import torch
import numpy as np
import pandas as pd
from typing import List, Dict, Tuple
import matplotlib.pyplot as plt
import seaborn as sns
from Bio.Seq import Seq
try:
    from Bio.SeqUtils import GC
except ImportError:
    # 如果导入失败，使用自定义GC计算函数
    def GC(seq):
        """计算GC含量"""
        seq = seq.upper()
        gc_count = seq.count('G') + seq.count('C')
        return (gc_count / len(seq)) * 100 if len(seq) > 0 else 0
from sklearn.metrics import accuracy_score, precision_recall_fscore_support
import warnings
warnings.filterwarnings('ignore')

from esm2_codon_optimizer import ESM2CodonOptimizer, ECOLI_CODON_TABLE
from data_processor import CodonUsageAnalyzer

class CodonOptimizationEvaluator:
    """密码子优化模型评估器"""
    
    def __init__(self, model: ESM2CodonOptimizer, analyzer: CodonUsageAnalyzer, device: str = 'cpu'):
        self.model = model.to(device)
        self.analyzer = analyzer
        self.device = device
        
    def evaluate_sequence_accuracy(self, protein_sequences: List[str], 
                                 true_nucleotide_sequences: List[str]) -> Dict:
        """评估序列级别的准确率"""
        self.model.eval()
        
        results = {
            'codon_accuracy': [],
            'nucleotide_accuracy': [],
            'protein_accuracy': [],
            'gc_content_similarity': [],
            'cai_scores': [],
            'sequence_lengths': []
        }
        
        with torch.no_grad():
            for protein_seq, true_nt_seq in zip(protein_sequences, true_nucleotide_sequences):
                # 预测最优密码子
                predicted_codons = self.model.predict_optimal_codons(protein_seq)
                predicted_nt_seq = ''.join(predicted_codons)
                
                # 转换真实序列为密码子
                true_codons = [true_nt_seq[i:i+3] for i in range(0, len(true_nt_seq), 3)]
                
                # 计算密码子准确率
                codon_acc = self._calculate_codon_accuracy(predicted_codons, true_codons)
                results['codon_accuracy'].append(codon_acc)
                
                # 计算核苷酸准确率
                nt_acc = self._calculate_nucleotide_accuracy(predicted_nt_seq, true_nt_seq)
                results['nucleotide_accuracy'].append(nt_acc)
                
                # 验证蛋白质序列一致性
                predicted_protein = str(Seq(predicted_nt_seq).translate())
                protein_acc = 1.0 if predicted_protein == protein_seq else 0.0
                results['protein_accuracy'].append(protein_acc)
                
                # 计算GC含量相似性
                gc_sim = self._calculate_gc_similarity(predicted_nt_seq, true_nt_seq)
                results['gc_content_similarity'].append(gc_sim)
                
                # 计算CAI分数
                cai_score = self.analyzer.get_codon_adaptation_index(predicted_codons)
                results['cai_scores'].append(cai_score)
                
                results['sequence_lengths'].append(len(protein_seq))
                
        return results
        
    def _calculate_codon_accuracy(self, predicted_codons: List[str], true_codons: List[str]) -> float:
        """计算密码子级别准确率"""
        if len(predicted_codons) != len(true_codons):
            return 0.0
            
        correct = sum(1 for p, t in zip(predicted_codons, true_codons) if p == t)
        return correct / len(true_codons)
        
    def _calculate_nucleotide_accuracy(self, predicted_seq: str, true_seq: str) -> float:
        """计算核苷酸级别准确率"""
        if len(predicted_seq) != len(true_seq):
            return 0.0
            
        correct = sum(1 for p, t in zip(predicted_seq, true_seq) if p == t)
        return correct / len(true_seq)
        
    def _calculate_gc_similarity(self, predicted_seq: str, true_seq: str) -> float:
        """计算GC含量相似性"""
        pred_gc = GC(predicted_seq) / 100.0
        true_gc = GC(true_seq) / 100.0
        return 1.0 - abs(pred_gc - true_gc)
        
    def evaluate_codon_preferences(self, protein_sequences: List[str]) -> Dict:
        """评估密码子偏好性"""
        self.model.eval()
        
        predicted_codon_counts = {}
        aa_codon_predictions = {}
        
        with torch.no_grad():
            for protein_seq in protein_sequences:
                predicted_codons = self.model.predict_optimal_codons(protein_seq)
                
                for aa, codon in zip(protein_seq, predicted_codons):
                    # 统计预测的密码子
                    predicted_codon_counts[codon] = predicted_codon_counts.get(codon, 0) + 1
                    
                    # 统计氨基酸-密码子对应
                    if aa not in aa_codon_predictions:
                        aa_codon_predictions[aa] = {}
                    aa_codon_predictions[aa][codon] = aa_codon_predictions[aa].get(codon, 0) + 1
                    
        # 计算预测的密码子频率
        total_predicted = sum(predicted_codon_counts.values())
        predicted_frequencies = {
            codon: count / total_predicted 
            for codon, count in predicted_codon_counts.items()
        }
        
        # 与真实频率比较
        frequency_correlation = self._calculate_frequency_correlation(
            predicted_frequencies, self.analyzer.codon_frequencies
        )
        
        return {
            'predicted_frequencies': predicted_frequencies,
            'frequency_correlation': frequency_correlation,
            'aa_codon_predictions': aa_codon_predictions
        }
        
    def _calculate_frequency_correlation(self, pred_freq: Dict, true_freq: Dict) -> float:
        """计算频率相关性"""
        common_codons = set(pred_freq.keys()) & set(true_freq.keys())
        
        if len(common_codons) < 2:
            return 0.0
            
        pred_values = [pred_freq[codon] for codon in common_codons]
        true_values = [true_freq[codon] for codon in common_codons]
        
        correlation = np.corrcoef(pred_values, true_values)[0, 1]
        return correlation if not np.isnan(correlation) else 0.0
        
    def benchmark_against_baselines(self, protein_sequences: List[str], 
                                  true_nucleotide_sequences: List[str]) -> Dict:
        """与基线方法对比"""
        
        # 我们的模型结果
        our_results = self.evaluate_sequence_accuracy(protein_sequences, true_nucleotide_sequences)
        
        # 高频选择基线 (HFC)
        hfc_results = self._evaluate_hfc_baseline(protein_sequences, true_nucleotide_sequences)
        
        # 随机选择基线
        random_results = self._evaluate_random_baseline(protein_sequences, true_nucleotide_sequences)
        
        # 背景频率选择基线 (BFC)
        bfc_results = self._evaluate_bfc_baseline(protein_sequences, true_nucleotide_sequences)
        
        return {
            'ESM2_CodonOptimizer': our_results,
            'High_Frequency_Choice': hfc_results,
            'Random_Choice': random_results,
            'Background_Frequency_Choice': bfc_results
        }
        
    def _evaluate_hfc_baseline(self, protein_sequences: List[str], 
                              true_nucleotide_sequences: List[str]) -> Dict:
        """高频选择基线评估"""
        results = {
            'codon_accuracy': [],
            'nucleotide_accuracy': [],
            'protein_accuracy': [],
            'gc_content_similarity': [],
            'cai_scores': []
        }
        
        for protein_seq, true_nt_seq in zip(protein_sequences, true_nucleotide_sequences):
            # 为每个氨基酸选择最高频率的密码子
            predicted_codons = []
            for aa in protein_seq:
                if aa in ECOLI_CODON_TABLE:
                    # 获取该氨基酸的所有密码子
                    codons = ECOLI_CODON_TABLE[aa]
                    # 选择频率最高的密码子
                    best_codon = max(codons, key=lambda c: self.analyzer.codon_frequencies.get(c, 0))
                    predicted_codons.append(best_codon)
                    
            predicted_nt_seq = ''.join(predicted_codons)
            true_codons = [true_nt_seq[i:i+3] for i in range(0, len(true_nt_seq), 3)]
            
            # 计算各项指标
            results['codon_accuracy'].append(
                self._calculate_codon_accuracy(predicted_codons, true_codons)
            )
            results['nucleotide_accuracy'].append(
                self._calculate_nucleotide_accuracy(predicted_nt_seq, true_nt_seq)
            )
            
            predicted_protein = str(Seq(predicted_nt_seq).translate())
            results['protein_accuracy'].append(
                1.0 if predicted_protein == protein_seq else 0.0
            )
            
            results['gc_content_similarity'].append(
                self._calculate_gc_similarity(predicted_nt_seq, true_nt_seq)
            )
            
            results['cai_scores'].append(
                self.analyzer.get_codon_adaptation_index(predicted_codons)
            )
            
        return results
        
    def _evaluate_random_baseline(self, protein_sequences: List[str], 
                                 true_nucleotide_sequences: List[str]) -> Dict:
        """随机选择基线评估"""
        results = {
            'codon_accuracy': [],
            'nucleotide_accuracy': [],
            'protein_accuracy': [],
            'gc_content_similarity': [],
            'cai_scores': []
        }
        
        for protein_seq, true_nt_seq in zip(protein_sequences, true_nucleotide_sequences):
            # 为每个氨基酸随机选择密码子
            predicted_codons = []
            for aa in protein_seq:
                if aa in ECOLI_CODON_TABLE:
                    codons = ECOLI_CODON_TABLE[aa]
                    predicted_codons.append(np.random.choice(codons))
                    
            predicted_nt_seq = ''.join(predicted_codons)
            true_codons = [true_nt_seq[i:i+3] for i in range(0, len(true_nt_seq), 3)]
            
            # 计算各项指标
            results['codon_accuracy'].append(
                self._calculate_codon_accuracy(predicted_codons, true_codons)
            )
            results['nucleotide_accuracy'].append(
                self._calculate_nucleotide_accuracy(predicted_nt_seq, true_nt_seq)
            )
            
            predicted_protein = str(Seq(predicted_nt_seq).translate())
            results['protein_accuracy'].append(
                1.0 if predicted_protein == protein_seq else 0.0
            )
            
            results['gc_content_similarity'].append(
                self._calculate_gc_similarity(predicted_nt_seq, true_nt_seq)
            )
            
            results['cai_scores'].append(
                self.analyzer.get_codon_adaptation_index(predicted_codons)
            )
            
        return results
        
    def _evaluate_bfc_baseline(self, protein_sequences: List[str], 
                              true_nucleotide_sequences: List[str]) -> Dict:
        """背景频率选择基线评估"""
        results = {
            'codon_accuracy': [],
            'nucleotide_accuracy': [],
            'protein_accuracy': [],
            'gc_content_similarity': [],
            'cai_scores': []
        }
        
        for protein_seq, true_nt_seq in zip(protein_sequences, true_nucleotide_sequences):
            # 根据背景频率概率选择密码子
            predicted_codons = []
            for aa in protein_seq:
                if aa in ECOLI_CODON_TABLE and aa in self.analyzer.aa_codon_preferences:
                    codons = list(self.analyzer.aa_codon_preferences[aa].keys())
                    probs = list(self.analyzer.aa_codon_preferences[aa].values())
                    predicted_codons.append(np.random.choice(codons, p=probs))
                elif aa in ECOLI_CODON_TABLE:
                    codons = ECOLI_CODON_TABLE[aa]
                    predicted_codons.append(np.random.choice(codons))
                    
            predicted_nt_seq = ''.join(predicted_codons)
            true_codons = [true_nt_seq[i:i+3] for i in range(0, len(true_nt_seq), 3)]
            
            # 计算各项指标
            results['codon_accuracy'].append(
                self._calculate_codon_accuracy(predicted_codons, true_codons)
            )
            results['nucleotide_accuracy'].append(
                self._calculate_nucleotide_accuracy(predicted_nt_seq, true_nt_seq)
            )
            
            predicted_protein = str(Seq(predicted_nt_seq).translate())
            results['protein_accuracy'].append(
                1.0 if predicted_protein == protein_seq else 0.0
            )
            
            results['gc_content_similarity'].append(
                self._calculate_gc_similarity(predicted_nt_seq, true_nt_seq)
            )
            
            results['cai_scores'].append(
                self.analyzer.get_codon_adaptation_index(predicted_codons)
            )
            
        return results
        
    def plot_evaluation_results(self, benchmark_results: Dict, save_path: str = None):
        """绘制评估结果"""
        
        # 准备数据
        methods = list(benchmark_results.keys())
        metrics = ['codon_accuracy', 'nucleotide_accuracy', 'protein_accuracy', 
                  'gc_content_similarity', 'cai_scores']
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        for i, metric in enumerate(metrics):
            data = []
            labels = []
            
            for method in methods:
                values = benchmark_results[method][metric]
                data.extend(values)
                labels.extend([method] * len(values))
                
            # 创建DataFrame用于绘图
            df = pd.DataFrame({'Method': labels, 'Value': data})
            
            # 箱线图
            sns.boxplot(data=df, x='Method', y='Value', ax=axes[i])
            axes[i].set_title(f'{metric.replace("_", " ").title()}')
            axes[i].tick_params(axis='x', rotation=45)
            
        # 移除多余的子图
        if len(metrics) < len(axes):
            for j in range(len(metrics), len(axes)):
                fig.delaxes(axes[j])
                
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            
        plt.show()
        
        # 打印统计摘要
        self._print_benchmark_summary(benchmark_results)
        
    def _print_benchmark_summary(self, benchmark_results: Dict):
        """打印基准测试摘要"""
        print("\n" + "="*60)
        print("BENCHMARK RESULTS SUMMARY")
        print("="*60)
        
        metrics = ['codon_accuracy', 'nucleotide_accuracy', 'protein_accuracy', 
                  'gc_content_similarity', 'cai_scores']
        
        for metric in metrics:
            print(f"\n{metric.replace('_', ' ').title()}:")
            print("-" * 40)
            
            for method, results in benchmark_results.items():
                values = results[metric]
                mean_val = np.mean(values)
                std_val = np.std(values)
                print(f"{method:25s}: {mean_val:.4f} ± {std_val:.4f}")
                
        print("\n" + "="*60)
