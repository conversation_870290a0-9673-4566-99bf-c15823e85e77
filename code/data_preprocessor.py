#!/usr/bin/env python3
"""
数据预处理脚本：修复终止密码子问题，确保核酸长度是蛋白质长度的3倍
"""

import pandas as pd
import numpy as np
from Bio.Seq import Seq
from tqdm import tqdm

def preprocess_bl21_data(input_file: str, output_file: str):
    """
    预处理BL21数据：
    1. 移除终止密码子，确保核酸长度 = 蛋白质长度 × 3
    2. 验证翻译一致性
    3. 重新计算长度
    """
    
    print(f"读取原始数据: {input_file}")
    data = pd.read_csv(input_file)
    print(f"原始数据条数: {len(data)}")
    
    processed_data = []
    
    for idx, row in tqdm(data.iterrows(), total=len(data), desc="处理数据"):
        protein_seq = row['protein_sequence']
        nucleotide_seq = row['nucleotide_sequence']
        
        # 检查是否以终止密码子结尾
        stop_codons = ['TAA', 'TAG', 'TGA']
        last_codon = nucleotide_seq[-3:]
        
        if last_codon in stop_codons:
            # 移除终止密码子
            nucleotide_seq_clean = nucleotide_seq[:-3]
        else:
            nucleotide_seq_clean = nucleotide_seq
            
        # 验证长度关系
        if len(nucleotide_seq_clean) != len(protein_seq) * 3:
            print(f"警告：序列 {idx} 长度不匹配，跳过")
            continue
            
        # 验证翻译
        try:
            translated = str(Seq(nucleotide_seq_clean).translate())
            if translated != protein_seq:
                print(f"警告：序列 {idx} 翻译不匹配，跳过")
                continue
        except Exception as e:
            print(f"警告：序列 {idx} 翻译错误 ({e})，跳过")
            continue
            
        # 添加到处理后的数据
        processed_data.append({
            'protein_sequence': protein_seq,
            'nucleotide_sequence': nucleotide_seq_clean,
            'protein_length': len(protein_seq),
            'nucleotide_length': len(nucleotide_seq_clean)
        })
        
    # 创建新的DataFrame
    processed_df = pd.DataFrame(processed_data)
    
    print(f"处理后数据条数: {len(processed_df)}")
    print(f"平均蛋白质长度: {processed_df['protein_length'].mean():.1f}")
    print(f"平均核酸长度: {processed_df['nucleotide_length'].mean():.1f}")
    print(f"长度比例检查: {(processed_df['nucleotide_length'] / processed_df['protein_length']).unique()}")
    
    # 保存处理后的数据
    processed_df.to_csv(output_file, index=False)
    print(f"处理后数据已保存到: {output_file}")
    
    return processed_df

if __name__ == "__main__":
    # 处理数据
    processed_data = preprocess_bl21_data(
        input_file="processed_BL21_data.csv",
        output_file="processed_BL21_data_clean.csv"
    )
    
    # 验证处理结果
    print("\n验证前5条数据:")
    for i in range(min(5, len(processed_data))):
        row = processed_data.iloc[i]
        protein_seq = row['protein_sequence']
        nucleotide_seq = row['nucleotide_sequence']
        
        print(f"\n序列 {i+1}:")
        print(f"  蛋白质长度: {len(protein_seq)}")
        print(f"  核酸长度: {len(nucleotide_seq)}")
        print(f"  长度比例: {len(nucleotide_seq) / len(protein_seq):.1f}")
        
        # 验证翻译
        translated = str(Seq(nucleotide_seq).translate())
        print(f"  翻译正确: {translated == protein_seq}")
