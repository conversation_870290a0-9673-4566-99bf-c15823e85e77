"""
ESM2-Enhanced Codon Optimizer for E. coli
Integrates ESM2 protein features with codon optimization strategies
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import pickle
import os
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 尝试导入transformers，如果失败则使用简化版本
try:
    from transformers import EsmModel, EsmTokenizer
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("Warning: transformers not available, using simplified ESM2 simulation")
    TRANSFORMERS_AVAILABLE = False

# 大肠杆菌密码子表
ECOLI_CODON_TABLE = {
    'A': ['GCT', 'GCC', 'GCA', 'GCG'],
    'R': ['CGT', 'CGC', 'CGA', 'CGG', 'AGA', 'AGG'],
    'N': ['AAT', 'AAC'],
    'D': ['GAT', 'GAC'],
    'C': ['TGT', 'TGC'],
    'Q': ['CAA', 'CAG'],
    'E': ['GAA', 'GAG'],
    'G': ['GGT', 'GGC', 'GGA', 'GGG'],
    'H': ['CAT', 'CAC'],
    'I': ['ATT', 'ATC', 'ATA'],
    'L': ['TTA', 'TTG', 'CTT', 'CTC', 'CTA', 'CTG'],
    'K': ['AAA', 'AAG'],
    'M': ['ATG'],
    'F': ['TTT', 'TTC'],
    'P': ['CCT', 'CCC', 'CCA', 'CCG'],
    'S': ['TCT', 'TCC', 'TCA', 'TCG', 'AGT', 'AGC'],
    'T': ['ACT', 'ACC', 'ACA', 'ACG'],
    'W': ['TGG'],
    'Y': ['TAT', 'TAC'],
    'V': ['GTT', 'GTC', 'GTA', 'GTG'],
    '*': ['TAA', 'TAG', 'TGA']
}

class SimpleESM2Simulator(nn.Module):
    """简化的ESM2模拟器，用于在没有transformers时提供基本功能"""

    def __init__(self, vocab_size=33, hidden_size=1280, max_length=1024):
        super().__init__()
        self.vocab_size = vocab_size
        self.hidden_size = hidden_size
        self.max_length = max_length

        # 简单的嵌入层和编码器
        self.embedding = nn.Embedding(vocab_size, hidden_size)
        self.encoder = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(d_model=hidden_size, nhead=8, batch_first=True),
            num_layers=4
        )
        self.layer_norm = nn.LayerNorm(hidden_size)

    def forward(self, input_ids, attention_mask=None):
        # 简单的前向传播
        embeddings = self.embedding(input_ids)

        if attention_mask is not None:
            # 应用注意力掩码
            embeddings = embeddings * attention_mask.unsqueeze(-1)

        encoded = self.encoder(embeddings)
        output = self.layer_norm(encoded)

        # 返回类似transformers的输出格式
        class SimpleOutput:
            def __init__(self, last_hidden_state):
                self.last_hidden_state = last_hidden_state

        return SimpleOutput(output)

class SimpleTokenizer:
    """简化的分词器"""

    def __init__(self):
        # 氨基酸字母表
        self.vocab = ['<pad>', '<cls>', '<sep>', '<unk>'] + list('ARNDCQEGHILKMFPSTWYV*')
        self.vocab_to_id = {token: i for i, token in enumerate(self.vocab)}

    def __call__(self, sequences, return_tensors="pt", padding=True, truncation=True, max_length=1024):
        if isinstance(sequences, str):
            sequences = [sequences]

        # 转换为token ids
        input_ids = []
        attention_masks = []

        max_len = min(max_length, max(len(seq) + 2 for seq in sequences)) if padding else max_length

        for seq in sequences:
            # 添加特殊token
            tokens = ['<cls>'] + list(seq[:max_length-2]) + ['<sep>']

            # 转换为ids
            ids = [self.vocab_to_id.get(token, self.vocab_to_id['<unk>']) for token in tokens]

            # 填充
            if padding and len(ids) < max_len:
                attention_mask = [1] * len(ids) + [0] * (max_len - len(ids))
                ids = ids + [self.vocab_to_id['<pad>']] * (max_len - len(ids))
            else:
                attention_mask = [1] * len(ids)

            input_ids.append(ids)
            attention_masks.append(attention_mask)

        # 转换为tensor（默认在CPU上，稍后会移动到正确设备）
        result = {
            'input_ids': torch.tensor(input_ids, dtype=torch.long),
            'attention_mask': torch.tensor(attention_masks, dtype=torch.bool)
        }

        return result

# 创建氨基酸到索引的映射
AA_TO_IDX = {aa: i for i, aa in enumerate('ARNDCQEGHILKMFPSTWYV*')}
IDX_TO_AA = {i: aa for aa, i in AA_TO_IDX.items()}

# 创建密码子到索引的映射
ALL_CODONS = []
for aa, codons in ECOLI_CODON_TABLE.items():
    ALL_CODONS.extend(codons)
CODON_TO_IDX = {codon: i for i, codon in enumerate(ALL_CODONS)}
IDX_TO_CODON = {i: codon for codon, i in CODON_TO_IDX.items()}

class CrossModalFusion(nn.Module):
    """跨模态融合层：融合ESM2蛋白质特征和密码子序列特征"""
    
    def __init__(self, protein_dim=1280, codon_dim=512, fusion_dim=768):
        super().__init__()
        self.protein_dim = protein_dim
        self.codon_dim = codon_dim
        self.fusion_dim = fusion_dim
        
        # 特征投影层
        self.protein_proj = nn.Linear(protein_dim, fusion_dim)
        self.codon_proj = nn.Linear(codon_dim, fusion_dim)
        
        # 双向交叉注意力
        self.protein_to_codon_attn = nn.MultiheadAttention(fusion_dim, num_heads=8, batch_first=True)
        self.codon_to_protein_attn = nn.MultiheadAttention(fusion_dim, num_heads=8, batch_first=True)
        
        # 融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(fusion_dim * 2, fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(fusion_dim, fusion_dim)
        )
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(fusion_dim)
        
    def forward(self, protein_features, codon_features, attention_mask=None):
        """
        Args:
            protein_features: [batch_size, seq_len, protein_dim]
            codon_features: [batch_size, seq_len, codon_dim]
            attention_mask: [batch_size, seq_len]
        """
        # 特征投影
        protein_proj = self.protein_proj(protein_features)  # [B, L, fusion_dim]
        codon_proj = self.codon_proj(codon_features)        # [B, L, fusion_dim]
        
        # 交叉注意力
        p2c_attn, _ = self.protein_to_codon_attn(
            codon_proj, protein_proj, protein_proj, 
            key_padding_mask=~attention_mask if attention_mask is not None else None
        )
        c2p_attn, _ = self.codon_to_protein_attn(
            protein_proj, codon_proj, codon_proj,
            key_padding_mask=~attention_mask if attention_mask is not None else None
        )
        
        # 特征融合
        fused_features = torch.cat([p2c_attn, c2p_attn], dim=-1)  # [B, L, fusion_dim*2]
        output = self.fusion_layer(fused_features)                # [B, L, fusion_dim]
        output = self.layer_norm(output + codon_proj)             # 残差连接
        
        return output

class EcoliCodonPredictor(nn.Module):
    """大肠杆菌特异性密码子预测头 - 基于氨基酸的同义密码子选择"""

    def __init__(self, input_dim=768):
        super().__init__()

        # 为每个氨基酸创建独立的密码子选择器
        self.aa_codon_predictors = nn.ModuleDict()

        for aa, codons in ECOLI_CODON_TABLE.items():
            if aa != '*':  # 跳过终止密码子
                num_codons = len(codons)
                if num_codons > 1:  # 只为有多个密码子的氨基酸创建预测器
                    self.aa_codon_predictors[aa] = nn.Sequential(
                        nn.Linear(input_dim, input_dim // 2),
                        nn.ReLU(),
                        nn.Dropout(0.1),
                        nn.Linear(input_dim // 2, num_codons)
                    )

        # 氨基酸到索引的映射
        self.aa_to_idx = AA_TO_IDX

    def forward(self, x, amino_acid_sequence):
        """
        Args:
            x: 融合特征 [batch_size, seq_len, input_dim]
            amino_acid_sequence: 氨基酸序列列表 [batch_size]
        Returns:
            codon_logits: 每个位置的密码子选择概率 [batch_size, seq_len, max_codons]
        """
        batch_size, seq_len, _ = x.shape

        # 为每个位置预测最优密码子
        codon_predictions = []

        for b in range(batch_size):
            seq_predictions = []
            aa_seq = amino_acid_sequence[b]

            for pos in range(min(seq_len, len(aa_seq))):
                aa = aa_seq[pos]

                if aa in self.aa_codon_predictors:
                    # 有多个密码子选择的氨基酸
                    logits = self.aa_codon_predictors[aa](x[b, pos])  # [num_codons]
                    seq_predictions.append(logits)
                else:
                    # 只有一个密码子的氨基酸（如M, W）
                    codons = ECOLI_CODON_TABLE.get(aa, ['UNK'])
                    num_codons = len(codons)
                    # 创建确定性预测（只有一个选择）
                    logits = torch.zeros(num_codons, device=x.device)
                    if num_codons == 1:
                        logits[0] = 10.0  # 高置信度
                    seq_predictions.append(logits)

            codon_predictions.append(seq_predictions)

        return codon_predictions

class ESM2CodonOptimizer(nn.Module):
    """ESM2增强的大肠杆菌密码子优化模型"""
    
    def __init__(self, esm_model_path="/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D",
                 freeze_esm=True, fusion_dim=768):
        super().__init__()

        # ESM2蛋白质编码器
        if TRANSFORMERS_AVAILABLE:
            try:
                self.esm_model = EsmModel.from_pretrained(esm_model_path)
                self.esm_tokenizer = EsmTokenizer.from_pretrained(esm_model_path)
                print("✓ Loaded real ESM2 model")
            except Exception as e:
                print(f"Warning: Failed to load ESM2 model ({e}), using simulator")
                self.esm_model = SimpleESM2Simulator()
                self.esm_tokenizer = SimpleTokenizer()
        else:
            print("Using simplified ESM2 simulator")
            self.esm_model = SimpleESM2Simulator()
            self.esm_tokenizer = SimpleTokenizer()

        # 冻结ESM2参数（初期训练）
        if freeze_esm:
            for param in self.esm_model.parameters():
                param.requires_grad = False
        
        # 密码子序列编码器
        self.codon_embedding = nn.Embedding(len(ALL_CODONS) + 4, 512)  # +4 for special tokens
        self.codon_encoder = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(d_model=512, nhead=8, batch_first=True),
            num_layers=6
        )
        
        # 跨模态融合层
        self.cross_fusion = CrossModalFusion(
            protein_dim=1280,  # ESM2-650M特征维度
            codon_dim=512,
            fusion_dim=fusion_dim
        )
        
        # 大肠杆菌特异性密码子预测头
        self.codon_predictor = EcoliCodonPredictor(input_dim=fusion_dim)
        
        # 特殊token索引
        self.pad_token_id = len(ALL_CODONS)
        self.mask_token_id = len(ALL_CODONS) + 1
        self.cls_token_id = len(ALL_CODONS) + 2
        self.sep_token_id = len(ALL_CODONS) + 3
        
    def encode_protein_sequence(self, protein_sequences: List[str]):
        """编码蛋白质序列"""
        # 使用ESM2 tokenizer
        inputs = self.esm_tokenizer(
            protein_sequences,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=1024
        )

        # 移动输入到正确设备
        device = next(self.parameters()).device
        inputs = {k: v.to(device) for k, v in inputs.items()}

        # 获取ESM2特征
        with torch.no_grad() if not self.training else torch.enable_grad():
            outputs = self.esm_model(**inputs)
            protein_features = outputs.last_hidden_state  # [B, L, 1280]

        return protein_features, inputs['attention_mask']
        
    def encode_codon_sequence(self, codon_sequences: List[List[str]]):
        """编码密码子序列"""
        batch_size = len(codon_sequences)
        max_len = max(len(seq) for seq in codon_sequences)

        # 获取设备信息
        device = next(self.parameters()).device

        # 创建批次张量并移动到正确设备
        codon_ids = torch.full((batch_size, max_len), self.pad_token_id, dtype=torch.long, device=device)
        attention_mask = torch.zeros(batch_size, max_len, dtype=torch.bool, device=device)
        
        for i, seq in enumerate(codon_sequences):
            for j, codon in enumerate(seq):
                if codon in CODON_TO_IDX:
                    codon_ids[i, j] = CODON_TO_IDX[codon]
                    attention_mask[i, j] = True
                    
        # 嵌入和编码
        codon_embeddings = self.codon_embedding(codon_ids)  # [B, L, 512]
        codon_features = self.codon_encoder(codon_embeddings)  # [B, L, 512]
        
        return codon_features, attention_mask
        
    def forward(self, protein_sequences: List[str], codon_sequences: List[List[str]]):
        """前向传播"""
        # 编码蛋白质序列
        protein_features, protein_mask = self.encode_protein_sequence(protein_sequences)

        # 编码密码子序列
        codon_features, codon_mask = self.encode_codon_sequence(codon_sequences)

        # 确保序列长度匹配
        min_len = min(protein_features.size(1), codon_features.size(1))
        protein_features = protein_features[:, :min_len, :]
        codon_features = codon_features[:, :min_len, :]
        protein_mask = protein_mask[:, :min_len]
        codon_mask = codon_mask[:, :min_len]

        # 使用更严格的mask（两个都为True才为True）
        combined_mask = protein_mask & codon_mask

        # 跨模态融合
        fused_features = self.cross_fusion(
            protein_features, codon_features, combined_mask
        )

        # 基于氨基酸的密码子预测
        codon_predictions = self.codon_predictor(fused_features, protein_sequences)

        return codon_predictions, combined_mask
        
    def predict_optimal_codons(self, protein_sequence: str, temperature=1.0):
        """预测最优密码子序列 - 基于氨基酸的同义密码子选择"""
        self.eval()
        with torch.no_grad():
            # 初始化随机密码子序列
            initial_codons = []
            for aa in protein_sequence:
                if aa in ECOLI_CODON_TABLE:
                    # 随机选择一个密码子
                    codons = ECOLI_CODON_TABLE[aa]
                    initial_codons.append(np.random.choice(codons))

            # 前向传播
            codon_predictions, mask = self.forward([protein_sequence], [initial_codons])

            # 为每个位置选择最优密码子
            optimal_codons = []

            for i, aa in enumerate(protein_sequence):
                if aa in ECOLI_CODON_TABLE:
                    codons = ECOLI_CODON_TABLE[aa]

                    if len(codons) == 1:
                        # 只有一个密码子选择
                        optimal_codons.append(codons[0])
                    else:
                        # 有多个密码子选择，使用模型预测
                        if i < len(codon_predictions[0]):
                            logits = codon_predictions[0][i]  # 第一个样本的第i个位置

                            # 应用温度缩放
                            logits = logits / temperature

                            # 获取概率分布
                            probs = F.softmax(logits, dim=-1)

                            # 选择概率最高的密码子
                            best_idx = torch.argmax(probs).item()
                            if best_idx < len(codons):
                                optimal_codons.append(codons[best_idx])
                            else:
                                optimal_codons.append(codons[0])  # 默认选择
                        else:
                            optimal_codons.append(codons[0])  # 默认选择
                else:
                    # 未知氨基酸，跳过
                    continue

            return optimal_codons
