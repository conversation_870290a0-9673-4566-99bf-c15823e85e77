#!/bin/bash

echo "🔧 修复GPU支持脚本"
echo "===================="

# 激活conda环境
source /home/<USER>/database/anaconda3/etc/profile.d/conda.sh
conda activate codon

echo "1. 检查当前PyTorch版本..."
python -c "import torch; print(f'当前PyTorch版本: {torch.__version__}'); print(f'CUDA可用: {torch.cuda.is_available()}')"

echo ""
echo "2. 检测CUDA版本..."
nvcc --version | grep "release"

echo ""
echo "3. 卸载当前PyTorch..."
pip uninstall torch torchvision torchaudio -y

echo ""
echo "4. 安装PyTorch GPU版本 (CUDA 12.1兼容)..."
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

echo ""
echo "5. 验证GPU支持..."
python -c "
import torch
print(f'✓ PyTorch版本: {torch.__version__}')
print(f'✓ CUDA可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'✓ CUDA版本: {torch.version.cuda}')
    print(f'✓ GPU数量: {torch.cuda.device_count()}')
    for i in range(torch.cuda.device_count()):
        print(f'✓ GPU {i}: {torch.cuda.get_device_name(i)}')
        print(f'✓ GPU {i} 内存: {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB')
else:
    print('❌ CUDA仍不可用')
"

echo ""
echo "6. 测试GPU张量操作..."
python -c "
import torch
if torch.cuda.is_available():
    device = torch.device('cuda')
    x = torch.randn(1000, 1000).to(device)
    y = torch.randn(1000, 1000).to(device)
    z = torch.mm(x, y)
    print(f'✓ GPU张量运算测试成功: {z.shape} on {z.device}')
else:
    print('❌ 无法进行GPU测试')
"

echo ""
echo "🎉 GPU支持修复完成！"
